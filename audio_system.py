"""
Audio System for Bloons Tower Defense
Manages sound effects and background music.
"""

import pygame
import os
from typing import Dict, Optional
from enum import Enum

class SoundType(Enum):
    # UI Sounds
    BUTTON_CLICK = "button_click"
    TOWER_PLACE = "tower_place"
    TOWER_UPGRADE = "tower_upgrade"
    TOWER_SELL = "tower_sell"
    
    # Combat Sounds
    DART_SHOOT = "dart_shoot"
    TACK_SHOOT = "tack_shoot"
    SNIPER_SHOOT = "sniper_shoot"
    BOMB_SHOOT = "bomb_shoot"
    EXPLOSION = "explosion"
    
    # Bloon Sounds
    BLOON_POP = "bloon_pop"
    BLOON_LEAK = "bloon_leak"
    MOAB_POP = "moab_pop"
    
    # Wave Sounds
    WAVE_START = "wave_start"
    WAVE_COMPLETE = "wave_complete"
    
    # Game Sounds
    GAME_OVER = "game_over"
    VICTORY = "victory"

class MusicType(Enum):
    MENU = "menu"
    GAMEPLAY = "gameplay"
    BOSS = "boss"

class AudioManager:
    """Manages all audio in the game."""
    
    def __init__(self):
        # Initialize pygame mixer
        pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
        pygame.mixer.init()
        
        # Audio settings
        self.sound_volume = 0.7
        self.music_volume = 0.5
        self.enabled = True
        
        # Sound storage
        self.sounds: Dict[SoundType, pygame.mixer.Sound] = {}
        self.music_tracks: Dict[MusicType, str] = {}
        
        # Current music state
        self.current_music = None
        self.music_playing = False
        
        # Load audio files
        self._load_audio_files()
    
    def _load_audio_files(self):
        """Load all audio files."""
        # Define sound file mappings
        sound_files = {
            SoundType.BUTTON_CLICK: "button_click.wav",
            SoundType.TOWER_PLACE: "tower_place.wav",
            SoundType.TOWER_UPGRADE: "tower_upgrade.wav",
            SoundType.TOWER_SELL: "tower_sell.wav",
            SoundType.DART_SHOOT: "dart_shoot.wav",
            SoundType.TACK_SHOOT: "tack_shoot.wav",
            SoundType.SNIPER_SHOOT: "sniper_shoot.wav",
            SoundType.BOMB_SHOOT: "bomb_shoot.wav",
            SoundType.EXPLOSION: "explosion.wav",
            SoundType.BLOON_POP: "bloon_pop.wav",
            SoundType.BLOON_LEAK: "bloon_leak.wav",
            SoundType.MOAB_POP: "moab_pop.wav",
            SoundType.WAVE_START: "wave_start.wav",
            SoundType.WAVE_COMPLETE: "wave_complete.wav",
            SoundType.GAME_OVER: "game_over.wav",
            SoundType.VICTORY: "victory.wav",
        }
        
        # Define music file mappings
        music_files = {
            MusicType.MENU: "menu_music.ogg",
            MusicType.GAMEPLAY: "gameplay_music.ogg",
            MusicType.BOSS: "boss_music.ogg",
        }
        
        # Load sound effects
        sounds_dir = "sounds"
        for sound_type, filename in sound_files.items():
            filepath = os.path.join(sounds_dir, filename)
            try:
                if os.path.exists(filepath):
                    sound = pygame.mixer.Sound(filepath)
                    sound.set_volume(self.sound_volume)
                    self.sounds[sound_type] = sound
                else:
                    # Create a placeholder sound if file doesn't exist
                    self.sounds[sound_type] = self._create_placeholder_sound(sound_type)
            except pygame.error:
                # Create placeholder if loading fails
                self.sounds[sound_type] = self._create_placeholder_sound(sound_type)
        
        # Load music tracks
        music_dir = "music"
        for music_type, filename in music_files.items():
            filepath = os.path.join(music_dir, filename)
            if os.path.exists(filepath):
                self.music_tracks[music_type] = filepath
    
    def _create_placeholder_sound(self, sound_type: SoundType) -> pygame.mixer.Sound:
        """Create a placeholder sound when audio file is missing."""
        # Create a simple tone based on sound type
        duration = 0.1  # 100ms
        sample_rate = 22050
        
        # Different frequencies for different sound types
        frequency_map = {
            SoundType.BUTTON_CLICK: 800,
            SoundType.TOWER_PLACE: 600,
            SoundType.TOWER_UPGRADE: 900,
            SoundType.TOWER_SELL: 400,
            SoundType.DART_SHOOT: 1000,
            SoundType.TACK_SHOOT: 1200,
            SoundType.SNIPER_SHOOT: 1500,
            SoundType.BOMB_SHOOT: 300,
            SoundType.EXPLOSION: 200,
            SoundType.BLOON_POP: 1800,
            SoundType.BLOON_LEAK: 500,
            SoundType.MOAB_POP: 150,
            SoundType.WAVE_START: 700,
            SoundType.WAVE_COMPLETE: 1100,
            SoundType.GAME_OVER: 250,
            SoundType.VICTORY: 1300,
        }
        
        frequency = frequency_map.get(sound_type, 800)
        
        # Generate simple sine wave without numpy dependency
        frames = int(duration * sample_rate)

        # Create a simple beep sound
        try:
            import array
            import math

            # Create stereo sound data
            sound_data = array.array('h')  # 16-bit signed integers

            for i in range(frames):
                time = float(i) / sample_rate
                wave = math.sin(frequency * 2 * math.pi * time)
                # Apply envelope to avoid clicks
                envelope = min(1.0, i / (sample_rate * 0.01), (frames - i) / (sample_rate * 0.01))
                sample = int(wave * envelope * 0.3 * 32767)
                sound_data.append(sample)  # Left channel
                sound_data.append(sample)  # Right channel

            # Convert to pygame sound
            sound = pygame.sndarray.make_sound(sound_data)
            sound.set_volume(self.sound_volume)

            return sound
        except:
            # Fallback: create a very simple sound
            sound_data = array.array('h', [0] * (frames * 2))
            sound = pygame.sndarray.make_sound(sound_data)
            sound.set_volume(0.1)
            return sound
    
    def play_sound(self, sound_type: SoundType, volume_multiplier: float = 1.0):
        """Play a sound effect."""
        if not self.enabled or sound_type not in self.sounds:
            return
        
        sound = self.sounds[sound_type]
        sound.set_volume(self.sound_volume * volume_multiplier)
        sound.play()
    
    def play_music(self, music_type: MusicType, loop: bool = True, fade_in_ms: int = 1000):
        """Play background music."""
        if not self.enabled or music_type not in self.music_tracks:
            return
        
        # Stop current music if playing
        if self.music_playing:
            pygame.mixer.music.fadeout(500)
        
        # Load and play new music
        try:
            pygame.mixer.music.load(self.music_tracks[music_type])
            pygame.mixer.music.set_volume(self.music_volume)
            
            loops = -1 if loop else 0
            if fade_in_ms > 0:
                pygame.mixer.music.play(loops, fade_in_ms=fade_in_ms)
            else:
                pygame.mixer.music.play(loops)
            
            self.current_music = music_type
            self.music_playing = True
            
        except pygame.error:
            print(f"Could not load music: {self.music_tracks[music_type]}")
    
    def stop_music(self, fade_out_ms: int = 1000):
        """Stop background music."""
        if self.music_playing:
            if fade_out_ms > 0:
                pygame.mixer.music.fadeout(fade_out_ms)
            else:
                pygame.mixer.music.stop()
            
            self.music_playing = False
            self.current_music = None
    
    def pause_music(self):
        """Pause background music."""
        if self.music_playing:
            pygame.mixer.music.pause()
    
    def resume_music(self):
        """Resume background music."""
        if self.music_playing:
            pygame.mixer.music.unpause()
    
    def set_sound_volume(self, volume: float):
        """Set sound effects volume (0.0 to 1.0)."""
        self.sound_volume = max(0.0, min(1.0, volume))
        
        # Update all loaded sounds
        for sound in self.sounds.values():
            sound.set_volume(self.sound_volume)
    
    def set_music_volume(self, volume: float):
        """Set music volume (0.0 to 1.0)."""
        self.music_volume = max(0.0, min(1.0, volume))
        pygame.mixer.music.set_volume(self.music_volume)
    
    def set_enabled(self, enabled: bool):
        """Enable or disable all audio."""
        self.enabled = enabled
        
        if not enabled:
            self.stop_music(0)
            pygame.mixer.stop()  # Stop all sound effects
    
    def get_sound_volume(self) -> float:
        """Get current sound volume."""
        return self.sound_volume
    
    def get_music_volume(self) -> float:
        """Get current music volume."""
        return self.music_volume
    
    def is_enabled(self) -> bool:
        """Check if audio is enabled."""
        return self.enabled
    
    def is_music_playing(self) -> bool:
        """Check if music is currently playing."""
        return self.music_playing and pygame.mixer.music.get_busy()
    
    def update(self):
        """Update audio system (call once per frame)."""
        # Check if music has stopped
        if self.music_playing and not pygame.mixer.music.get_busy():
            self.music_playing = False
            self.current_music = None
    
    def cleanup(self):
        """Clean up audio resources."""
        pygame.mixer.stop()
        pygame.mixer.music.stop()
        pygame.mixer.quit()

# Audio event helpers for easy integration
class AudioEvents:
    """Helper class for triggering audio events."""
    
    def __init__(self, audio_manager: AudioManager):
        self.audio = audio_manager
    
    def on_button_click(self):
        """Play button click sound."""
        self.audio.play_sound(SoundType.BUTTON_CLICK)
    
    def on_tower_placed(self):
        """Play tower placement sound."""
        self.audio.play_sound(SoundType.TOWER_PLACE)
    
    def on_tower_upgraded(self):
        """Play tower upgrade sound."""
        self.audio.play_sound(SoundType.TOWER_UPGRADE)
    
    def on_tower_sold(self):
        """Play tower sell sound."""
        self.audio.play_sound(SoundType.TOWER_SELL)
    
    def on_dart_shot(self):
        """Play dart shooting sound."""
        self.audio.play_sound(SoundType.DART_SHOOT, 0.6)
    
    def on_tack_shot(self):
        """Play tack shooting sound."""
        self.audio.play_sound(SoundType.TACK_SHOOT, 0.5)
    
    def on_sniper_shot(self):
        """Play sniper shooting sound."""
        self.audio.play_sound(SoundType.SNIPER_SHOOT, 0.8)
    
    def on_bomb_shot(self):
        """Play bomb shooting sound."""
        self.audio.play_sound(SoundType.BOMB_SHOOT, 0.7)
    
    def on_explosion(self):
        """Play explosion sound."""
        self.audio.play_sound(SoundType.EXPLOSION, 0.8)
    
    def on_bloon_popped(self, is_moab: bool = False):
        """Play bloon pop sound."""
        if is_moab:
            self.audio.play_sound(SoundType.MOAB_POP)
        else:
            self.audio.play_sound(SoundType.BLOON_POP, 0.4)
    
    def on_bloon_leaked(self):
        """Play bloon leak sound."""
        self.audio.play_sound(SoundType.BLOON_LEAK)
    
    def on_wave_started(self):
        """Play wave start sound."""
        self.audio.play_sound(SoundType.WAVE_START)
    
    def on_wave_completed(self):
        """Play wave complete sound."""
        self.audio.play_sound(SoundType.WAVE_COMPLETE)
    
    def on_game_over(self):
        """Play game over sound."""
        self.audio.play_sound(SoundType.GAME_OVER)
    
    def on_victory(self):
        """Play victory sound."""
        self.audio.play_sound(SoundType.VICTORY)
