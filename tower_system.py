"""
Tower System for Bloons Tower Defense
Manages all tower types, placement, targeting, and upgrades.
"""

import pygame
import math
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass
from bloon_system import Bloon

class TowerType(Enum):
    DART_MONKEY = "dart_monkey"
    TACK_SHOOTER = "tack_shooter"
    SNIPER_MONKEY = "sniper_monkey"
    BOMB_SHOOTER = "bomb_shooter"

class TargetingMode(Enum):
    FIRST = "first"      # First bloon on path
    LAST = "last"        # Last bloon on path
    CLOSE = "close"      # Closest bloon
    STRONG = "strong"    # Strongest bloon (most health)

@dataclass
class TowerProperties:
    """Base properties for tower types."""
    name: str
    cost: int
    damage: int
    range: float
    fire_rate: float  # Shots per second
    projectile_speed: float
    color: Tuple[int, int, int]
    size: int
    can_see_camo: bool = False
    damage_type: str = "sharp"  # "sharp", "explosive", "energy"

# Tower type definitions
TOWER_PROPERTIES = {
    TowerType.DART_MONKEY: TowerProperties(
        "Dart Monkey", 170, 1, 100, 1.0, 300, (139, 69, 19), 15
    ),
    TowerType.TACK_SHOOTER: TowerProperties(
        "Tack Shooter", 280, 1, 60, 1.2, 200, (255, 165, 0), 18
    ),
    TowerType.SNIPER_MONKEY: TowerProperties(
        "Sniper Monkey", 350, 2, 999, 0.6, 999, (0, 100, 0), 12
    ),
    TowerType.BOMB_SHOOTER: TowerProperties(
        "Bomb Shooter", 525, 1, 80, 0.8, 250, (64, 64, 64), 20, 
        damage_type="explosive"
    ),
}

@dataclass
class Upgrade:
    """Represents a tower upgrade."""
    name: str
    cost: int
    description: str
    effects: Dict[str, Any]  # Property changes

# Upgrade paths for each tower
TOWER_UPGRADES = {
    TowerType.DART_MONKEY: {
        "path1": [
            Upgrade("Sharp Darts", 120, "+1 damage", {"damage": 1}),
            Upgrade("Razor Sharp Darts", 300, "+2 damage", {"damage": 2}),
            Upgrade("Spike-o-pult", 2700, "Shoots spikes", {"damage": 3, "projectile_speed": 200}),
        ],
        "path2": [
            Upgrade("Long Range Darts", 150, "+15% range", {"range_multiplier": 1.15}),
            Upgrade("Enhanced Eyesight", 350, "+20% range, can see camo", 
                    {"range_multiplier": 1.2, "can_see_camo": True}),
            Upgrade("Crossbow Master", 7500, "Shoots crossbow bolts", 
                    {"damage": 10, "fire_rate": 2.0, "range_multiplier": 1.5}),
        ]
    },
    TowerType.TACK_SHOOTER: {
        "path1": [
            Upgrade("Faster Shooting", 100, "+33% attack speed", {"fire_rate_multiplier": 1.33}),
            Upgrade("Even Faster Shooting", 250, "+50% attack speed", {"fire_rate_multiplier": 1.5}),
            Upgrade("Tack Sprayer", 3800, "Shoots 16 tacks", {"projectile_count": 16}),
        ],
        "path2": [
            Upgrade("Long Range Tacks", 120, "+10% range", {"range_multiplier": 1.1}),
            Upgrade("Super Range Tacks", 500, "+25% range", {"range_multiplier": 1.25}),
            Upgrade("Blade Shooter", 4400, "Shoots blades", {"damage": 2, "range_multiplier": 1.4}),
        ]
    },
    TowerType.SNIPER_MONKEY: {
        "path1": [
            Upgrade("Full Metal Jacket", 350, "+1 damage", {"damage": 1}),
            Upgrade("Large Calibre", 1500, "+3 damage", {"damage": 3}),
            Upgrade("Deadly Precision", 4500, "+7 damage", {"damage": 7}),
        ],
        "path2": [
            Upgrade("Night Vision Goggles", 200, "Can see camo", {"can_see_camo": True}),
            Upgrade("Fast Firing", 800, "+50% attack speed", {"fire_rate_multiplier": 1.5}),
            Upgrade("Semi-Automatic", 3000, "+100% attack speed", {"fire_rate_multiplier": 2.0}),
        ]
    },
    TowerType.BOMB_SHOOTER: {
        "path1": [
            Upgrade("Bigger Bombs", 400, "+5 blast radius", {"blast_radius": 5}),
            Upgrade("Heavy Bombs", 650, "+1 damage", {"damage": 1}),
            Upgrade("MOAB Mauler", 2500, "+10 damage to MOAB class", {"moab_damage": 10}),
        ],
        "path2": [
            Upgrade("Missile Launcher", 550, "Seeks targets", {"seeking": True}),
            Upgrade("MOAB Assassin", 2500, "Ability: Massive damage to MOAB", {"ability": "moab_assassin"}),
            Upgrade("Bloon Annihilation", 8500, "Huge explosions", 
                    {"damage": 5, "blast_radius": 15, "fire_rate_multiplier": 1.5}),
        ]
    }
}

class Tower:
    """Individual tower instance."""
    
    def __init__(self, tower_type: TowerType, position: Tuple[float, float]):
        self.type = tower_type
        self.position = position
        self.properties = TOWER_PROPERTIES[tower_type].copy()
        
        # Upgrade tracking
        self.upgrades = {"path1": 0, "path2": 0}
        self.total_cost = self.properties.cost
        
        # Combat properties
        self.damage = self.properties.damage
        self.range = self.properties.range
        self.fire_rate = self.properties.fire_rate
        self.projectile_speed = self.properties.projectile_speed
        self.can_see_camo = self.properties.can_see_camo
        self.damage_type = self.properties.damage_type
        
        # Special properties
        self.blast_radius = 20 if tower_type == TowerType.BOMB_SHOOTER else 0
        self.projectile_count = 8 if tower_type == TowerType.TACK_SHOOTER else 1
        self.seeking = False
        self.moab_damage = 0
        
        # State
        self.targeting_mode = TargetingMode.FIRST
        self.last_shot_time = 0
        self.target = None
        self.angle = 0
        
        # Visual
        self.selected = False
    
    def can_upgrade(self, path: str) -> bool:
        """Check if tower can be upgraded on given path."""
        if path not in ["path1", "path2"]:
            return False
        
        current_level = self.upgrades[path]
        other_path = "path2" if path == "path1" else "path1"
        other_level = self.upgrades[other_path]
        
        # Can't upgrade if already at max level
        if current_level >= 3:
            return False
        
        # Can't upgrade if other path is at level 3
        if other_level >= 3:
            return False
        
        return True
    
    def get_upgrade_cost(self, path: str) -> int:
        """Get cost of next upgrade on path."""
        if not self.can_upgrade(path):
            return 0
        
        current_level = self.upgrades[path]
        upgrades = TOWER_UPGRADES[self.type][path]
        
        if current_level < len(upgrades):
            return upgrades[current_level].cost
        
        return 0
    
    def upgrade(self, path: str) -> bool:
        """Upgrade tower on given path."""
        if not self.can_upgrade(path):
            return False
        
        current_level = self.upgrades[path]
        upgrades = TOWER_UPGRADES[self.type][path]
        
        if current_level < len(upgrades):
            upgrade = upgrades[current_level]
            self.upgrades[path] += 1
            self.total_cost += upgrade.cost
            
            # Apply upgrade effects
            self._apply_upgrade_effects(upgrade.effects)
            return True
        
        return False
    
    def _apply_upgrade_effects(self, effects: Dict[str, Any]):
        """Apply upgrade effects to tower."""
        for effect, value in effects.items():
            if effect == "damage":
                self.damage += value
            elif effect == "range_multiplier":
                self.range *= value
            elif effect == "fire_rate_multiplier":
                self.fire_rate *= value
            elif effect == "can_see_camo":
                self.can_see_camo = value
            elif effect == "blast_radius":
                self.blast_radius += value
            elif effect == "projectile_count":
                self.projectile_count = value
            elif effect == "seeking":
                self.seeking = value
            elif effect == "moab_damage":
                self.moab_damage += value
            elif effect == "projectile_speed":
                self.projectile_speed = value
    
    def can_shoot(self, current_time: float) -> bool:
        """Check if tower can shoot based on fire rate."""
        time_since_last_shot = current_time - self.last_shot_time
        return time_since_last_shot >= (1.0 / self.fire_rate)
    
    def find_target(self, bloons: List[Bloon]) -> Optional[Bloon]:
        """Find target based on targeting mode."""
        # Filter bloons in range
        targets = []
        for bloon in bloons:
            if not bloon.alive:
                continue
            
            # Check camo detection
            if bloon.camo and not self.can_see_camo:
                continue
            
            # Check range (except for sniper)
            if self.type != TowerType.SNIPER_MONKEY:
                dx = bloon.position[0] - self.position[0]
                dy = bloon.position[1] - self.position[1]
                distance = math.sqrt(dx * dx + dy * dy)
                
                if distance > self.range:
                    continue
            
            targets.append(bloon)
        
        if not targets:
            return None
        
        # Apply targeting mode
        if self.targeting_mode == TargetingMode.FIRST:
            return max(targets, key=lambda b: b.distance_traveled)
        elif self.targeting_mode == TargetingMode.LAST:
            return min(targets, key=lambda b: b.distance_traveled)
        elif self.targeting_mode == TargetingMode.CLOSE:
            def distance_to_tower(bloon):
                dx = bloon.position[0] - self.position[0]
                dy = bloon.position[1] - self.position[1]
                return math.sqrt(dx * dx + dy * dy)
            return min(targets, key=distance_to_tower)
        elif self.targeting_mode == TargetingMode.STRONG:
            return max(targets, key=lambda b: b.health)
        
        return targets[0]
    
    def update(self, dt: float, bloons: List[Bloon], current_time: float, projectile_manager):
        """Update tower logic."""
        # Find target
        self.target = self.find_target(bloons)
        
        # Update angle to face target
        if self.target:
            dx = self.target.position[0] - self.position[0]
            dy = self.target.position[1] - self.position[1]
            self.angle = math.atan2(dy, dx)
        
        # Shoot if possible
        if self.target and self.can_shoot(current_time):
            self._shoot(projectile_manager, current_time)
    
    def _shoot(self, projectile_manager, current_time: float):
        """Create projectile(s) towards target."""
        self.last_shot_time = current_time
        
        if self.type == TowerType.TACK_SHOOTER:
            # Shoot in all directions
            for i in range(self.projectile_count):
                angle = (2 * math.pi * i) / self.projectile_count
                projectile_manager.create_projectile(
                    self.position, angle, self.projectile_speed,
                    self.damage, self.damage_type, self.blast_radius,
                    seeking=False, tower=self
                )
        else:
            # Shoot at target
            projectile_manager.create_projectile(
                self.position, self.angle, self.projectile_speed,
                self.damage, self.damage_type, self.blast_radius,
                seeking=self.seeking, tower=self, target=self.target
            )
    
    def get_sell_value(self) -> int:
        """Get money returned when selling tower."""
        return int(self.total_cost * 0.7)  # 70% of total cost
    
    def render(self, screen: pygame.Surface):
        """Render tower on screen."""
        x, y = int(self.position[0]), int(self.position[1])
        
        # Draw range circle if selected
        if self.selected and self.type != TowerType.SNIPER_MONKEY:
            pygame.draw.circle(screen, (255, 255, 255, 50), (x, y), int(self.range), 2)
        
        # Draw tower base
        pygame.draw.circle(screen, self.properties.color, (x, y), self.properties.size)
        pygame.draw.circle(screen, (0, 0, 0), (x, y), self.properties.size, 2)
        
        # Draw barrel/direction indicator
        if self.target or self.type == TowerType.TACK_SHOOTER:
            if self.type == TowerType.TACK_SHOOTER:
                # Draw multiple barrels for tack shooter
                for i in range(8):
                    angle = (2 * math.pi * i) / 8
                    end_x = x + int((self.properties.size - 3) * math.cos(angle))
                    end_y = y + int((self.properties.size - 3) * math.sin(angle))
                    pygame.draw.line(screen, (0, 0, 0), (x, y), (end_x, end_y), 3)
            else:
                # Draw single barrel
                barrel_length = self.properties.size + 5
                end_x = x + int(barrel_length * math.cos(self.angle))
                end_y = y + int(barrel_length * math.sin(self.angle))
                pygame.draw.line(screen, (0, 0, 0), (x, y), (end_x, end_y), 4)
        
        # Draw upgrade indicators
        path1_level = self.upgrades["path1"]
        path2_level = self.upgrades["path2"]
        
        if path1_level > 0:
            for i in range(path1_level):
                pygame.draw.circle(screen, (255, 0, 0), 
                                 (x - 8, y - self.properties.size - 5 - i * 3), 2)
        
        if path2_level > 0:
            for i in range(path2_level):
                pygame.draw.circle(screen, (0, 0, 255),
                                 (x + 8, y - self.properties.size - 5 - i * 3), 2)

class TowerManager:
    """Manages all towers in the game."""

    def __init__(self, path_manager):
        self.path_manager = path_manager
        self.towers: List[Tower] = []
        self.selected_tower: Optional[Tower] = None

    def can_place_tower(self, position: Tuple[float, float], tower_type: TowerType) -> bool:
        """Check if a tower can be placed at the given position."""
        x, y = position
        tower_size = TOWER_PROPERTIES[tower_type].size

        # Check if position is on path
        if self.path_manager.is_position_on_path(position, tower_size):
            return False

        # Check if position overlaps with existing towers
        for tower in self.towers:
            dx = tower.position[0] - x
            dy = tower.position[1] - y
            distance = math.sqrt(dx * dx + dy * dy)
            min_distance = tower.properties.size + tower_size + 5  # 5 pixel buffer

            if distance < min_distance:
                return False

        # Check if position is within screen bounds
        if (x < tower_size or x > 1200 - tower_size or
            y < tower_size or y > 800 - tower_size):
            return False

        return True

    def place_tower(self, tower_type: TowerType, position: Tuple[float, float]) -> bool:
        """Place a tower at the given position."""
        if self.can_place_tower(position, tower_type):
            tower = Tower(tower_type, position)
            self.towers.append(tower)
            return True
        return False

    def select_tower(self, position: Tuple[float, float]) -> Optional[Tower]:
        """Select tower at position."""
        for tower in self.towers:
            dx = tower.position[0] - position[0]
            dy = tower.position[1] - position[1]
            distance = math.sqrt(dx * dx + dy * dy)

            if distance <= tower.properties.size:
                # Deselect previous tower
                if self.selected_tower:
                    self.selected_tower.selected = False

                # Select new tower
                tower.selected = True
                self.selected_tower = tower
                return tower

        # Deselect if clicking empty space
        if self.selected_tower:
            self.selected_tower.selected = False
            self.selected_tower = None

        return None

    def sell_tower(self, tower: Tower) -> int:
        """Sell a tower and return money earned."""
        if tower in self.towers:
            self.towers.remove(tower)
            if self.selected_tower == tower:
                self.selected_tower = None
            return tower.get_sell_value()
        return 0

    def upgrade_tower(self, tower: Tower, path: str) -> int:
        """Upgrade a tower and return cost."""
        if tower in self.towers:
            cost = tower.get_upgrade_cost(path)
            if tower.upgrade(path):
                return cost
        return 0

    def update(self, dt: float, bloons: List[Bloon], current_time: float, projectile_manager):
        """Update all towers."""
        for tower in self.towers:
            tower.update(dt, bloons, current_time, projectile_manager)

    def render(self, screen: pygame.Surface):
        """Render all towers."""
        for tower in self.towers:
            tower.render(screen)

    def clear_all(self):
        """Remove all towers."""
        self.towers.clear()
        self.selected_tower = None
