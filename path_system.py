"""
Path System for Bloons Tower Defense
Manages the path that bloons follow from start to end.
"""

import pygame
import math
from typing import List, Tuple

class PathManager:
    """Manages the path that bloons follow."""
    
    def __init__(self):
        # Define waypoints for the path (x, y coordinates)
        self.waypoints = [
            (50, 400),    # Start point
            (200, 400),
            (200, 200),
            (400, 200),
            (400, 600),
            (600, 600),
            (600, 100),
            (800, 100),
            (800, 500),
            (1000, 500),
            (1000, 300),
            (1150, 300)   # End point
        ]
        
        # Calculate path segments and total length
        self.path_segments = []
        self.total_length = 0
        self._calculate_path_segments()
        
        # Path width for rendering and collision
        self.path_width = 40
    
    def _calculate_path_segments(self):
        """Calculate path segments and cumulative distances."""
        cumulative_distance = 0
        
        for i in range(len(self.waypoints) - 1):
            start = self.waypoints[i]
            end = self.waypoints[i + 1]
            
            # Calculate segment length
            dx = end[0] - start[0]
            dy = end[1] - start[1]
            length = math.sqrt(dx * dx + dy * dy)
            
            # Store segment info
            segment = {
                'start': start,
                'end': end,
                'length': length,
                'start_distance': cumulative_distance,
                'end_distance': cumulative_distance + length,
                'direction': (dx / length if length > 0 else 0, 
                            dy / length if length > 0 else 0)
            }
            
            self.path_segments.append(segment)
            cumulative_distance += length
        
        self.total_length = cumulative_distance
    
    def get_position_at_distance(self, distance: float) -> Tuple[float, float]:
        """Get the position on the path at a given distance from start."""
        if distance <= 0:
            return self.waypoints[0]
        if distance >= self.total_length:
            return self.waypoints[-1]
        
        # Find which segment contains this distance
        for segment in self.path_segments:
            if distance <= segment['end_distance']:
                # Calculate position within this segment
                segment_progress = distance - segment['start_distance']
                progress_ratio = segment_progress / segment['length']
                
                start_x, start_y = segment['start']
                end_x, end_y = segment['end']
                
                x = start_x + (end_x - start_x) * progress_ratio
                y = start_y + (end_y - start_y) * progress_ratio
                
                return (x, y)
        
        return self.waypoints[-1]
    
    def get_direction_at_distance(self, distance: float) -> Tuple[float, float]:
        """Get the direction vector at a given distance on the path."""
        # Find which segment contains this distance
        for segment in self.path_segments:
            if distance <= segment['end_distance']:
                return segment['direction']
        
        # Return direction of last segment if past the end
        return self.path_segments[-1]['direction'] if self.path_segments else (1, 0)
    
    def is_position_on_path(self, pos: Tuple[float, float], tolerance: float = None) -> bool:
        """Check if a position is on the path within tolerance."""
        if tolerance is None:
            tolerance = self.path_width / 2
        
        x, y = pos
        
        # Check distance to each path segment
        for segment in self.path_segments:
            start_x, start_y = segment['start']
            end_x, end_y = segment['end']
            
            # Calculate distance from point to line segment
            dist = self._point_to_line_distance(x, y, start_x, start_y, end_x, end_y)
            
            if dist <= tolerance:
                return True
        
        return False
    
    def _point_to_line_distance(self, px: float, py: float, 
                               x1: float, y1: float, x2: float, y2: float) -> float:
        """Calculate the shortest distance from a point to a line segment."""
        # Vector from start to end of line
        dx = x2 - x1
        dy = y2 - y1
        
        if dx == 0 and dy == 0:
            # Line segment is a point
            return math.sqrt((px - x1) ** 2 + (py - y1) ** 2)
        
        # Parameter t represents position along the line segment
        t = ((px - x1) * dx + (py - y1) * dy) / (dx * dx + dy * dy)
        
        # Clamp t to [0, 1] to stay within the line segment
        t = max(0, min(1, t))
        
        # Find the closest point on the line segment
        closest_x = x1 + t * dx
        closest_y = y1 + t * dy
        
        # Return distance to closest point
        return math.sqrt((px - closest_x) ** 2 + (py - closest_y) ** 2)
    
    def get_start_position(self) -> Tuple[float, float]:
        """Get the starting position of the path."""
        return self.waypoints[0]
    
    def get_end_position(self) -> Tuple[float, float]:
        """Get the ending position of the path."""
        return self.waypoints[-1]
    
    def render(self, screen: pygame.Surface):
        """Render the path on the screen."""
        # Draw path segments
        for i in range(len(self.waypoints) - 1):
            start = self.waypoints[i]
            end = self.waypoints[i + 1]
            
            # Draw path line
            pygame.draw.line(screen, (139, 69, 19), start, end, self.path_width)
        
        # Draw waypoints for debugging (optional)
        for i, waypoint in enumerate(self.waypoints):
            color = (255, 0, 0) if i == 0 else (0, 0, 255) if i == len(self.waypoints) - 1 else (255, 255, 0)
            pygame.draw.circle(screen, color, waypoint, 8)
        
        # Draw path borders
        for i in range(len(self.waypoints) - 1):
            start = self.waypoints[i]
            end = self.waypoints[i + 1]
            pygame.draw.line(screen, (101, 67, 33), start, end, self.path_width + 4)
            pygame.draw.line(screen, (139, 69, 19), start, end, self.path_width)
