"""
Projectile System for Bloons Tower Defense
Manages all projectiles, collision detection, and damage application.
"""

import pygame
import math
from typing import List, Tu<PERSON>, Optional
from bloon_system import Bloon

class Projectile:
    """Individual projectile instance."""
    
    def __init__(self, position: Tuple[float, float], angle: float, speed: float,
                 damage: int, damage_type: str = "sharp", blast_radius: float = 0,
                 seeking: bool = False, tower=None, target: Optional[Bloon] = None):
        self.position = list(position)
        self.angle = angle
        self.speed = speed
        self.damage = damage
        self.damage_type = damage_type
        self.blast_radius = blast_radius
        self.seeking = seeking
        self.tower = tower
        self.target = target
        
        # Calculate velocity
        self.velocity = [
            math.cos(angle) * speed,
            math.sin(angle) * speed
        ]
        
        # State
        self.alive = True
        self.distance_traveled = 0
        self.max_distance = 800  # Max range before projectile disappears
        
        # Visual properties
        self.size = 3
        self.color = self._get_projectile_color()
        
        # Trail effect for some projectiles
        self.trail_positions = []
        self.max_trail_length = 5
    
    def _get_projectile_color(self) -> <PERSON><PERSON>[int, int, int]:
        """Get projectile color based on damage type."""
        if self.damage_type == "explosive":
            return (255, 100, 0)  # Orange
        elif self.damage_type == "energy":
            return (0, 255, 255)  # Cyan
        elif self.damage_type == "freeze":
            return (100, 200, 255)  # Light blue
        else:
            return (139, 69, 19)  # Brown for sharp/normal
    
    def update(self, dt: float, bloons: List[Bloon]) -> Tuple[bool, int]:
        """
        Update projectile position and check for collisions.
        Returns (hit_something, money_earned)
        """
        if not self.alive:
            return False, 0
        
        # Update trail
        self.trail_positions.append(tuple(self.position))
        if len(self.trail_positions) > self.max_trail_length:
            self.trail_positions.pop(0)
        
        # Seeking behavior
        if self.seeking and self.target and self.target.alive:
            self._update_seeking()
        
        # Move projectile
        move_distance = self.speed * dt
        self.position[0] += self.velocity[0] * dt
        self.position[1] += self.velocity[1] * dt
        self.distance_traveled += move_distance
        
        # Check if projectile has traveled too far
        if self.distance_traveled > self.max_distance:
            self.alive = False
            return False, 0
        
        # Check for collisions
        return self._check_collisions(bloons)
    
    def _update_seeking(self):
        """Update projectile direction for seeking projectiles."""
        if not self.target or not self.target.alive:
            return
        
        # Calculate direction to target
        dx = self.target.position[0] - self.position[0]
        dy = self.target.position[1] - self.position[1]
        distance = math.sqrt(dx * dx + dy * dy)
        
        if distance > 0:
            # Update angle towards target
            target_angle = math.atan2(dy, dx)
            
            # Smooth turning (not instant)
            angle_diff = target_angle - self.angle
            
            # Normalize angle difference to [-pi, pi]
            while angle_diff > math.pi:
                angle_diff -= 2 * math.pi
            while angle_diff < -math.pi:
                angle_diff += 2 * math.pi
            
            # Turn towards target (max turn rate)
            max_turn_rate = 5.0  # radians per second
            turn_amount = max(-max_turn_rate * (1/60), min(max_turn_rate * (1/60), angle_diff))
            self.angle += turn_amount
            
            # Update velocity
            self.velocity[0] = math.cos(self.angle) * self.speed
            self.velocity[1] = math.sin(self.angle) * self.speed
    
    def _check_collisions(self, bloons: List[Bloon]) -> Tuple[bool, int]:
        """Check for collisions with bloons."""
        money_earned = 0
        
        for bloon in bloons:
            if not bloon.alive:
                continue
            
            # Check collision
            dx = bloon.position[0] - self.position[0]
            dy = bloon.position[1] - self.position[1]
            distance = math.sqrt(dx * dx + dy * dy)
            
            if distance <= bloon.radius + self.size:
                # Hit!
                if self.blast_radius > 0:
                    # Explosive damage - damage all bloons in blast radius
                    money_earned += self._apply_blast_damage(bloons)
                else:
                    # Single target damage
                    children, reward = bloon.take_damage(self.damage, self.damage_type)
                    money_earned += reward
                    
                    # Add children to bloon list (handled by bloon manager)
                    # This is a bit of a hack - in a real implementation,
                    # we'd return the children to be added by the manager
                
                self.alive = False
                return True, money_earned
        
        return False, money_earned
    
    def _apply_blast_damage(self, bloons: List[Bloon]) -> int:
        """Apply blast damage to all bloons in radius."""
        money_earned = 0
        blast_center = tuple(self.position)
        
        for bloon in bloons:
            if not bloon.alive:
                continue
            
            # Check if bloon is in blast radius
            dx = bloon.position[0] - blast_center[0]
            dy = bloon.position[1] - blast_center[1]
            distance = math.sqrt(dx * dx + dy * dy)
            
            if distance <= self.blast_radius + bloon.radius:
                children, reward = bloon.take_damage(self.damage, self.damage_type)
                money_earned += reward
        
        return money_earned
    
    def render(self, screen: pygame.Surface):
        """Render projectile on screen."""
        if not self.alive:
            return
        
        x, y = int(self.position[0]), int(self.position[1])
        
        # Draw trail for some projectiles
        if len(self.trail_positions) > 1:
            for i, pos in enumerate(self.trail_positions[:-1]):
                alpha = int(255 * (i + 1) / len(self.trail_positions))
                trail_color = (*self.color, alpha)
                trail_size = max(1, int(self.size * (i + 1) / len(self.trail_positions)))
                pygame.draw.circle(screen, self.color, (int(pos[0]), int(pos[1])), trail_size)
        
        # Draw main projectile
        pygame.draw.circle(screen, self.color, (x, y), self.size)
        
        # Draw outline for visibility
        outline_color = (0, 0, 0) if self.color != (0, 0, 0) else (255, 255, 255)
        pygame.draw.circle(screen, outline_color, (x, y), self.size, 1)
        
        # Draw blast radius preview for explosive projectiles (when close to target)
        if self.blast_radius > 0 and self.target:
            dx = self.target.position[0] - self.position[0]
            dy = self.target.position[1] - self.position[1]
            distance = math.sqrt(dx * dx + dy * dy)
            
            if distance < 50:  # Show blast radius when close
                pygame.draw.circle(screen, (255, 0, 0, 50), (x, y), int(self.blast_radius), 2)

class ProjectileManager:
    """Manages all projectiles in the game."""
    
    def __init__(self):
        self.projectiles: List[Projectile] = []
    
    def create_projectile(self, position: Tuple[float, float], angle: float, speed: float,
                         damage: int, damage_type: str = "sharp", blast_radius: float = 0,
                         seeking: bool = False, tower=None, target: Optional[Bloon] = None):
        """Create a new projectile."""
        projectile = Projectile(position, angle, speed, damage, damage_type, 
                              blast_radius, seeking, tower, target)
        self.projectiles.append(projectile)
    
    def update(self, dt: float, bloons: List[Bloon]) -> int:
        """Update all projectiles and return total money earned."""
        total_money = 0
        
        for projectile in self.projectiles[:]:  # Copy list to avoid modification issues
            hit, money = projectile.update(dt, bloons)
            total_money += money
        
        # Remove dead projectiles
        self.projectiles = [p for p in self.projectiles if p.alive]
        
        return total_money
    
    def render(self, screen: pygame.Surface):
        """Render all projectiles."""
        for projectile in self.projectiles:
            projectile.render(screen)
    
    def clear_all(self):
        """Remove all projectiles."""
        self.projectiles.clear()

class EffectManager:
    """Manages visual effects like explosions."""
    
    def __init__(self):
        self.effects = []
    
    def create_explosion(self, position: Tuple[float, float], radius: float):
        """Create an explosion effect."""
        effect = {
            'type': 'explosion',
            'position': position,
            'radius': radius,
            'time': 0.3,  # Duration in seconds
            'max_time': 0.3
        }
        self.effects.append(effect)
    
    def update(self, dt: float):
        """Update all effects."""
        for effect in self.effects[:]:
            effect['time'] -= dt
            if effect['time'] <= 0:
                self.effects.remove(effect)
    
    def render(self, screen: pygame.Surface):
        """Render all effects."""
        for effect in self.effects:
            if effect['type'] == 'explosion':
                self._render_explosion(screen, effect)
    
    def _render_explosion(self, screen: pygame.Surface, effect):
        """Render explosion effect."""
        progress = 1.0 - (effect['time'] / effect['max_time'])
        x, y = int(effect['position'][0]), int(effect['position'][1])
        
        # Expanding circle
        current_radius = int(effect['radius'] * progress)
        alpha = int(255 * (1.0 - progress))
        
        # Draw multiple circles for explosion effect
        for i in range(3):
            radius = current_radius - i * 3
            if radius > 0:
                color_intensity = 255 - i * 50
                color = (color_intensity, color_intensity // 2, 0)  # Orange to red
                pygame.draw.circle(screen, color, (x, y), radius, 2)
    
    def clear_all(self):
        """Remove all effects."""
        self.effects.clear()
