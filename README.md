# Bloons Tower Defense - Python Game

A fully functional tower defense game inspired by the Bloons series, built with Python and Pygame.

## Features

### 🎮 Core Gameplay
- **2D top-down tower defense** with predefined bloon paths
- **Multiple tower types** with unique abilities and upgrade paths
- **Diverse bloon types** with different properties and behaviors
- **Progressive wave system** with increasing difficulty
- **Currency and life management** systems

### 🎈 Bloon Types
- **Red Bloons**: Basic bloons with 1 HP
- **Blue Bloons**: Faster than red, 2 HP total
- **Green Bloons**: Even faster, 3 HP total
- **Yellow Bloons**: Very fast, 4 HP total
- **Pink Bloons**: Extremely fast, 5 HP total
- **Black/White Bloons**: Special properties, split into multiple bloons
- **Lead Bloons**: Immune to sharp projectiles
- **Zebra/Rainbow Bloons**: High-tier bloons with multiple layers
- **Ceramic Bloons**: Tough bloons requiring multiple hits
- **MOAB-class Bloons**: Massive bloons with very high health

### 🗼 Tower Types
1. **Dart Monkey** ($170)
   - Shoots single darts at bloons
   - Upgrade Path 1: Increased damage and spike projectiles
   - Upgrade Path 2: Extended range and camo detection

2. **Tack Shooter** ($280)
   - Shoots tacks in 8 directions
   - Upgrade Path 1: Faster shooting and more projectiles
   - Upgrade Path 2: Longer range and blade projectiles

3. **Sniper Monkey** ($350)
   - Long-range precision shots
   - Upgrade Path 1: Increased damage and armor penetration
   - Upgrade Path 2: Camo detection and faster firing

4. **Bomb Shooter** ($525)
   - Area-of-effect explosive damage
   - Upgrade Path 1: Bigger explosions and MOAB damage
   - Upgrade Path 2: Seeking missiles and special abilities

### 🎯 Tower Features
- **Targeting modes**: First, Last, Close, Strong
- **Upgrade system**: Two paths with 3 levels each
- **Range visualization** when placing or selecting towers
- **Sell towers** for 70% of total investment

### 🌊 Wave System
- **100+ waves** with progressive difficulty
- **Early waves** (1-10): Basic bloon types
- **Mid waves** (11-25): Special bloons and camo introduction
- **Late waves** (26-40): Ceramic bloons and first MOABs
- **MOAB era** (41+): Procedurally generated high-difficulty waves

### 🎵 Audio System
- **Sound effects** for all game actions
- **Background music** for different game states
- **Volume controls** for music and sound effects
- **Fallback audio** when sound files are missing

### ⚙️ Optional Features
- **Save/Load system** with quicksave (F5) and quickload (F9)
- **Difficulty modes**: Easy, Medium, Hard
- **Performance monitor** with FPS display (F1)
- **Cheat system** for testing (F12 to enable)
- **Settings management** with persistent preferences

## Installation

1. **Install Python 3.7+**
2. **Install Pygame**:
   ```bash
   pip install pygame
   ```
3. **Run the game**:
   ```bash
   python main.py
   ```

## Controls

### Basic Controls
- **SPACE**: Start game / Start next wave / Restart after game over
- **ESC**: Pause/unpause game
- **Mouse**: Place towers, select towers, interact with UI

### Advanced Controls
- **F1**: Toggle FPS display
- **F5**: Quick save (during gameplay)
- **F9**: Quick load
- **F12**: Enable cheat system

### Cheat Codes (after pressing F12)
Type these during gameplay:
- `money`: Add $10,000
- `lives`: Add 100 lives
- `wave`: Skip to next wave
- `clear`: Clear all bloons
- `god`: Infinite lives

## File Structure

```
BTDpygame/
├── main.py              # Main game file and game loop
├── path_system.py       # Path management and bloon movement
├── bloon_system.py      # Bloon types and behavior
├── tower_system.py      # Tower mechanics and upgrades
├── projectile_system.py # Projectile physics and collision
├── wave_system.py       # Wave spawning and progression
├── ui_system.py         # User interface and controls
├── audio_system.py      # Sound effects and music
├── game_features.py     # Save/load, settings, cheats
├── requirements.txt     # Python dependencies
├── sounds/              # Sound effect files (optional)
├── music/               # Background music files (optional)
└── saves/               # Save game files
```

## Audio Files (Optional)

The game will work without audio files, but for the full experience, add these files:

### Sound Effects (`sounds/` directory)
- `button_click.wav` - UI button clicks
- `tower_place.wav` - Tower placement
- `tower_upgrade.wav` - Tower upgrades
- `tower_sell.wav` - Tower selling
- `dart_shoot.wav` - Dart monkey shooting
- `tack_shoot.wav` - Tack shooter firing
- `sniper_shoot.wav` - Sniper shots
- `bomb_shoot.wav` - Bomb shooter firing
- `explosion.wav` - Bomb explosions
- `bloon_pop.wav` - Regular bloon popping
- `bloon_leak.wav` - Bloons reaching the end
- `moab_pop.wav` - MOAB-class bloon destruction
- `wave_start.wav` - Wave beginning
- `wave_complete.wav` - Wave completion
- `game_over.wav` - Game over sound
- `victory.wav` - Victory sound

### Background Music (`music/` directory)
- `menu_music.ogg` - Main menu background music
- `gameplay_music.ogg` - In-game background music
- `boss_music.ogg` - MOAB wave music (optional)

**Note**: The game generates placeholder sounds if audio files are missing, so it's fully playable without any audio files.

## Gameplay Tips

1. **Start with Dart Monkeys** - They're cost-effective for early waves
2. **Use Tack Shooters** at path corners for maximum coverage
3. **Upgrade strategically** - Focus on one upgrade path per tower
4. **Plan for camo bloons** - Upgrade towers for camo detection
5. **Save money for MOAB waves** - Bomb Shooters are effective against them
6. **Use targeting modes** - Switch between First/Last/Close/Strong as needed

## Technical Details

- **Built with**: Python 3.7+ and Pygame 2.5+
- **Performance**: Optimized for 60 FPS gameplay
- **Resolution**: 1200x800 pixels
- **Architecture**: Modular system design for easy expansion
- **Save format**: JSON files for cross-platform compatibility

## Customization

The game is designed to be easily modifiable:

- **Add new bloon types** in `bloon_system.py`
- **Create new towers** in `tower_system.py`
- **Modify wave progression** in `wave_system.py`
- **Adjust difficulty** in `game_features.py`
- **Add new sound effects** in `audio_system.py`

## License

This project is created for educational purposes. Feel free to modify and expand upon it!

## Credits

Inspired by the Bloons Tower Defense series by Ninja Kiwi. This is an independent recreation built from scratch for learning purposes.
