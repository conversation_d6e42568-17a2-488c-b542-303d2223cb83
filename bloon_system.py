"""
Bloon System for Bloons Tower Defense
Manages all bloon types, movement, and behavior.
"""

import pygame
import math
from typing import List, Tuple, Optional
from enum import Enum
from dataclasses import dataclass

class BloonType(Enum):
    RED = "red"
    BLUE = "blue"
    GREEN = "green"
    YELLOW = "yellow"
    PINK = "pink"
    BLACK = "black"
    WHITE = "white"
    LEAD = "lead"
    ZEBRA = "zebra"
    RAINBOW = "rainbow"
    CERAMIC = "ceramic"
    MOAB = "moab"
    BFG = "bfg"
    ZOMG = "zomg"
    BAD = "bad"

@dataclass
class BloonProperties:
    """Properties for different bloon types."""
    health: int
    speed: float
    reward: int
    color: Tuple[int, int, int]
    radius: int
    children: List[BloonType]  # What bloons spawn when this one is popped
    camo: bool = False
    lead: bool = False  # Immune to sharp projectiles
    ceramic: bool = False  # Takes multiple hits to break ceramic layer

# Bloon type definitions
BLOON_PROPERTIES = {
    BloonType.RED: BloonProperties(1, 1.0, 1, (255, 0, 0), 8, []),
    BloonType.BLUE: BloonProperties(1, 1.4, 1, (0, 0, 255), 8, [BloonType.RED]),
    BloonType.GREEN: BloonProperties(1, 1.8, 1, (0, 255, 0), 8, [BloonType.BLUE]),
    BloonType.YELLOW: BloonProperties(1, 3.2, 1, (255, 255, 0), 8, [BloonType.GREEN]),
    BloonType.PINK: BloonProperties(1, 3.5, 1, (255, 192, 203), 8, [BloonType.YELLOW]),
    BloonType.BLACK: BloonProperties(1, 1.8, 1, (0, 0, 0), 8, [BloonType.PINK, BloonType.PINK]),
    BloonType.WHITE: BloonProperties(1, 2.0, 1, (255, 255, 255), 8, [BloonType.PINK, BloonType.PINK]),
    BloonType.LEAD: BloonProperties(1, 1.0, 2, (128, 128, 128), 8, [BloonType.BLACK, BloonType.BLACK], lead=True),
    BloonType.ZEBRA: BloonProperties(1, 1.8, 1, (255, 255, 255), 8, [BloonType.BLACK, BloonType.WHITE]),
    BloonType.RAINBOW: BloonProperties(1, 2.2, 1, (128, 0, 128), 8, [BloonType.ZEBRA, BloonType.ZEBRA]),
    BloonType.CERAMIC: BloonProperties(10, 2.5, 2, (139, 69, 19), 10, [BloonType.RAINBOW, BloonType.RAINBOW], ceramic=True),
    BloonType.MOAB: BloonProperties(200, 1.0, 100, (0, 100, 0), 20, [BloonType.CERAMIC] * 4),
    BloonType.BFG: BloonProperties(700, 0.25, 300, (255, 0, 0), 25, [BloonType.MOAB] * 4),
    BloonType.ZOMG: BloonProperties(4000, 0.18, 1000, (0, 255, 255), 30, [BloonType.BFG] * 4),
    BloonType.BAD: BloonProperties(20000, 0.18, 5000, (128, 0, 128), 35, [BloonType.ZOMG] * 2 + [BloonType.MOAB] * 3),
}

class Bloon:
    """Individual bloon instance."""
    
    def __init__(self, bloon_type: BloonType, path_manager, camo: bool = False):
        self.type = bloon_type
        self.properties = BLOON_PROPERTIES[bloon_type]
        self.path_manager = path_manager
        
        # Position and movement
        self.distance_traveled = 0.0
        self.position = path_manager.get_start_position()
        self.speed = self.properties.speed
        
        # Health and status
        self.health = self.properties.health
        self.max_health = self.properties.health
        self.alive = True
        self.reached_end = False
        
        # Special properties
        self.camo = camo or self.properties.camo
        self.lead = self.properties.lead
        self.ceramic = self.properties.ceramic
        
        # Visual properties
        self.radius = self.properties.radius
        self.color = self.properties.color
        
        # Animation
        self.rotation = 0.0
    
    def update(self, dt: float) -> List['Bloon']:
        """Update bloon position and return any spawned children."""
        if not self.alive:
            return []
        
        # Move along path
        move_distance = self.speed * 50 * dt  # Scale speed
        self.distance_traveled += move_distance
        
        # Update position
        self.position = self.path_manager.get_position_at_distance(self.distance_traveled)
        
        # Check if reached end
        if self.distance_traveled >= self.path_manager.total_length:
            self.reached_end = True
            self.alive = False
        
        # Update rotation for visual effect
        self.rotation += dt * 2.0
        
        return []
    
    def take_damage(self, damage: int, damage_type: str = "normal") -> Tuple[List['Bloon'], int]:
        """
        Apply damage to bloon and return spawned children and reward.
        damage_type can be: "normal", "explosive", "freeze", "sharp"
        """
        # Lead bloons are immune to sharp damage
        if self.lead and damage_type == "sharp":
            return [], 0
        
        # Apply damage
        self.health -= damage
        
        # Check if bloon is destroyed
        if self.health <= 0:
            self.alive = False
            children = self._spawn_children()
            reward = self.properties.reward
            return children, reward
        
        return [], 0
    
    def _spawn_children(self) -> List['Bloon']:
        """Spawn child bloons when this bloon is destroyed."""
        children = []
        
        for child_type in self.properties.children:
            child = Bloon(child_type, self.path_manager, self.camo)
            child.distance_traveled = self.distance_traveled
            child.position = self.position
            children.append(child)
        
        return children
    
    def get_center(self) -> Tuple[float, float]:
        """Get the center position of the bloon."""
        return self.position
    
    def render(self, screen: pygame.Surface):
        """Render the bloon on screen."""
        if not self.alive:
            return
        
        x, y = int(self.position[0]), int(self.position[1])
        
        # Draw bloon body
        pygame.draw.circle(screen, self.color, (x, y), self.radius)
        
        # Draw outline
        outline_color = (0, 0, 0) if self.color != (0, 0, 0) else (255, 255, 255)
        pygame.draw.circle(screen, outline_color, (x, y), self.radius, 2)
        
        # Draw camo pattern if camo bloon
        if self.camo:
            for i in range(3):
                angle = self.rotation + i * (2 * math.pi / 3)
                stripe_x = x + int(self.radius * 0.6 * math.cos(angle))
                stripe_y = y + int(self.radius * 0.6 * math.sin(angle))
                pygame.draw.circle(screen, (0, 100, 0), (stripe_x, stripe_y), 2)
        
        # Draw lead shine if lead bloon
        if self.lead:
            shine_x = x - self.radius // 3
            shine_y = y - self.radius // 3
            pygame.draw.circle(screen, (200, 200, 200), (shine_x, shine_y), 3)
        
        # Draw health bar for stronger bloons
        if self.max_health > 1:
            bar_width = self.radius * 2
            bar_height = 4
            bar_x = x - bar_width // 2
            bar_y = y - self.radius - 8
            
            # Background
            pygame.draw.rect(screen, (255, 0, 0), (bar_x, bar_y, bar_width, bar_height))
            
            # Health
            health_width = int(bar_width * (self.health / self.max_health))
            pygame.draw.rect(screen, (0, 255, 0), (bar_x, bar_y, health_width, bar_height))

class BloonManager:
    """Manages all bloons in the game."""
    
    def __init__(self, path_manager):
        self.path_manager = path_manager
        self.bloons: List[Bloon] = []
        self.bloons_reached_end = 0
    
    def add_bloon(self, bloon_type: BloonType, camo: bool = False):
        """Add a new bloon to the game."""
        bloon = Bloon(bloon_type, self.path_manager, camo)
        self.bloons.append(bloon)
    
    def update(self, dt: float) -> Tuple[int, int]:
        """Update all bloons and return (lives_lost, money_earned)."""
        lives_lost = 0
        money_earned = 0
        new_bloons = []
        
        # Update existing bloons
        for bloon in self.bloons[:]:  # Copy list to avoid modification during iteration
            if not bloon.alive:
                continue
            
            children = bloon.update(dt)
            new_bloons.extend(children)
            
            # Check if bloon reached end
            if bloon.reached_end:
                lives_lost += 1
                self.bloons_reached_end += 1
        
        # Add new bloons from splitting
        self.bloons.extend(new_bloons)
        
        # Remove dead bloons
        self.bloons = [bloon for bloon in self.bloons if bloon.alive]
        
        return lives_lost, money_earned
    
    def damage_bloons_in_area(self, center: Tuple[float, float], radius: float, 
                             damage: int, damage_type: str = "normal") -> int:
        """Damage all bloons in a circular area and return money earned."""
        money_earned = 0
        new_bloons = []
        
        for bloon in self.bloons:
            if not bloon.alive:
                continue
            
            # Check if bloon is in damage area
            dx = bloon.position[0] - center[0]
            dy = bloon.position[1] - center[1]
            distance = math.sqrt(dx * dx + dy * dy)
            
            if distance <= radius + bloon.radius:
                children, reward = bloon.take_damage(damage, damage_type)
                new_bloons.extend(children)
                money_earned += reward
        
        # Add new bloons from splitting
        self.bloons.extend(new_bloons)
        
        return money_earned
    
    def get_bloons_in_range(self, center: Tuple[float, float], radius: float, 
                           can_see_camo: bool = False) -> List[Bloon]:
        """Get all bloons within range of a point."""
        bloons_in_range = []
        
        for bloon in self.bloons:
            if not bloon.alive:
                continue
            
            # Skip camo bloons if tower can't see them
            if bloon.camo and not can_see_camo:
                continue
            
            # Check distance
            dx = bloon.position[0] - center[0]
            dy = bloon.position[1] - center[1]
            distance = math.sqrt(dx * dx + dy * dy)
            
            if distance <= radius:
                bloons_in_range.append(bloon)
        
        return bloons_in_range
    
    def render(self, screen: pygame.Surface):
        """Render all bloons."""
        for bloon in self.bloons:
            bloon.render(screen)
    
    def clear_all(self):
        """Remove all bloons."""
        self.bloons.clear()
        self.bloons_reached_end = 0
