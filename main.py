#!/usr/bin/env python3
"""
Bloons Tower Defense - Main Game File
A complete tower defense game inspired by the Bloons series.
"""

import pygame
import sys
import math
import json
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass
import os

# Import game systems
from path_system import PathManager
from bloon_system import BloonManager, BloonType
from tower_system import TowerManager, TowerType
from projectile_system import ProjectileManager, Effect<PERSON>anager
from wave_system import WaveManager
from ui_system import UIManager
from audio_system import AudioManager, AudioEvents
from game_features import SaveLoadManager, GameSettings, PerformanceMonitor, CheatManager, Difficulty, DIFFICULTY_SETTINGS

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
PURPLE = (128, 0, 128)
ORANGE = (255, 165, 0)
BROWN = (139, 69, 19)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
LIGHT_GRAY = (192, 192, 192)
PINK = (255, 192, 203)

# Game States
class GameState(Enum):
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    GAME_OVER = "game_over"
    VICTORY = "victory"

class Game:
    """Main game class that manages all game systems."""
    
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Bloons Tower Defense")
        self.clock = pygame.time.Clock()
        self.running = True
        self.state = GameState.MENU
        
        # Game systems will be initialized here
        self.path_manager = None
        self.bloon_manager = None
        self.tower_manager = None
        self.projectile_manager = None
        self.wave_manager = None
        self.ui_manager = None
        self.audio_manager = None
        
        # Game variables
        self.difficulty = Difficulty.MEDIUM
        self.money = 650  # Starting money
        self.lives = 100  # Starting lives
        self.current_wave = 0
        self.selected_tower_type = None
        self.selected_tower = None

        # Optional features
        self.save_load_manager = SaveLoadManager()
        self.settings = GameSettings()
        self.performance_monitor = PerformanceMonitor()
        self.cheat_manager = CheatManager()

        # Apply settings
        self.performance_monitor.show_fps = self.settings.get("show_fps", False)
        
        # Initialize all systems
        self._initialize_systems()
    
    def _initialize_systems(self):
        """Initialize all game systems."""
        # Initialize core systems
        self.path_manager = PathManager()
        self.bloon_manager = BloonManager(self.path_manager)
        self.tower_manager = TowerManager(self.path_manager)
        self.projectile_manager = ProjectileManager()
        self.effect_manager = EffectManager()
        self.wave_manager = WaveManager(self.bloon_manager)
        self.audio_manager = AudioManager()
        self.audio_events = AudioEvents(self.audio_manager)
        self.ui_manager = UIManager(self)

        # Apply settings to audio
        self.settings.apply_to_game(self)

        # Start menu music
        from audio_system import MusicType
        self.audio_manager.play_music(MusicType.MENU)
    
    def handle_events(self):
        """Handle all pygame events."""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.state == GameState.PLAYING:
                        self.state = GameState.PAUSED
                    elif self.state == GameState.PAUSED:
                        self.state = GameState.PLAYING
                elif event.key == pygame.K_SPACE:
                    if self.state == GameState.MENU:
                        self.state = GameState.PLAYING
                        self.start_new_game()
                    elif self.state in [GameState.GAME_OVER, GameState.VICTORY]:
                        self.state = GameState.MENU
                        self.start_new_game()
                        from audio_system import MusicType
                        self.audio_manager.play_music(MusicType.MENU)
                elif event.key == pygame.K_F1:
                    # Toggle FPS display
                    self.performance_monitor.show_fps = not self.performance_monitor.show_fps
                elif event.key == pygame.K_F5:
                    # Quick save
                    if self.state == GameState.PLAYING:
                        self.save_load_manager.save_game(self)
                elif event.key == pygame.K_F9:
                    # Quick load
                    if self.save_load_manager.load_game(self):
                        self.state = GameState.PLAYING
                elif event.key == pygame.K_F12:
                    # Enable cheats (for testing)
                    self.cheat_manager.enable()

            # Handle cheat input
            self.cheat_manager.handle_input(event, self)
            elif event.type == pygame.MOUSEBUTTONDOWN:
                self._handle_mouse_click(event)
    
    def _handle_mouse_click(self, event):
        """Handle mouse click events."""
        if self.state == GameState.PLAYING:
            # Let UI manager handle the click
            self.ui_manager.handle_event(event)
    
    def update(self, dt):
        """Update all game systems."""
        if self.state == GameState.PLAYING:
            current_time = pygame.time.get_ticks() / 1000.0

            # Update bloons and handle lives lost
            lives_lost, money_from_bloons = self.bloon_manager.update(dt)
            self.lives -= lives_lost
            self.money += money_from_bloons

            if lives_lost > 0:
                self.audio_events.on_bloon_leaked()

            # Update towers
            self.tower_manager.update(dt, self.bloon_manager.bloons, current_time, self.projectile_manager)

            # Update projectiles and handle money earned
            money_from_projectiles = self.projectile_manager.update(dt, self.bloon_manager.bloons)
            self.money += money_from_projectiles

            # Update effects
            self.effect_manager.update(dt)

            # Update wave manager
            wave_result = self.wave_manager.update(dt)
            if wave_result['wave_started']:
                self.audio_events.on_wave_started()
            if wave_result['wave_completed']:
                self.audio_events.on_wave_completed()
                self.money += wave_result['money_bonus']

            # Update UI
            self.ui_manager.update(dt)

            # Update audio
            self.audio_manager.update()

            # Update performance monitor
            self.performance_monitor.update(self.clock)

            # Check game over conditions
            if self.lives <= 0:
                self.state = GameState.GAME_OVER
                self.audio_events.on_game_over()
                self.audio_manager.stop_music()

            # Check victory condition (completed all waves)
            wave_info = self.wave_manager.get_wave_info()
            if wave_info['wave_number'] > wave_info['total_waves'] and not wave_info['in_progress']:
                self.state = GameState.VICTORY
                self.audio_events.on_victory()
                self.audio_manager.stop_music()
    
    def render(self):
        """Render all game elements."""
        self.screen.fill(GREEN)  # Background
        
        if self.state == GameState.MENU:
            self._render_menu()
        elif self.state in [GameState.PLAYING, GameState.PAUSED]:
            self._render_game()
            if self.state == GameState.PAUSED:
                self._render_pause_overlay()
        elif self.state in [GameState.GAME_OVER, GameState.VICTORY]:
            self._render_game_over()
        
        pygame.display.flip()
    
    def _render_menu(self):
        """Render the main menu."""
        font = pygame.font.Font(None, 74)
        title = font.render("Bloons Tower Defense", True, BLACK)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 100))
        self.screen.blit(title, title_rect)
        
        font = pygame.font.Font(None, 36)
        start_text = font.render("Press SPACE to Start", True, BLACK)
        start_rect = start_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        self.screen.blit(start_text, start_rect)
    
    def _render_game(self):
        """Render the main game screen."""
        # Render path
        self.path_manager.render(self.screen)

        # Render bloons
        self.bloon_manager.render(self.screen)

        # Render towers
        self.tower_manager.render(self.screen)

        # Render projectiles
        self.projectile_manager.render(self.screen)

        # Render effects
        self.effect_manager.render(self.screen)

        # Render UI
        self.ui_manager.render(self.screen)

        # Render performance info
        self.performance_monitor.render(self.screen)
    
    def _render_pause_overlay(self):
        """Render pause overlay."""
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))
        
        font = pygame.font.Font(None, 74)
        pause_text = font.render("PAUSED", True, WHITE)
        pause_rect = pause_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        self.screen.blit(pause_text, pause_rect)
    
    def _render_game_over(self):
        """Render game over screen."""
        self.screen.fill(BLACK)
        font = pygame.font.Font(None, 74)

        if self.state == GameState.GAME_OVER:
            game_over_text = font.render("GAME OVER", True, RED)
            game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
            self.screen.blit(game_over_text, game_over_rect)

            # Show final stats
            stats_font = pygame.font.Font(None, 36)
            wave_text = stats_font.render(f"Reached Wave: {self.wave_manager.current_wave}", True, WHITE)
            wave_rect = wave_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 60))
            self.screen.blit(wave_text, wave_rect)

        elif self.state == GameState.VICTORY:
            victory_text = font.render("VICTORY!", True, GREEN)
            victory_rect = victory_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
            self.screen.blit(victory_text, victory_rect)

            # Show final stats
            stats_font = pygame.font.Font(None, 36)
            money_text = stats_font.render(f"Final Money: ${self.money}", True, WHITE)
            money_rect = money_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 60))
            self.screen.blit(money_text, money_rect)

        # Show restart instruction
        restart_font = pygame.font.Font(None, 28)
        restart_text = restart_font.render("Press SPACE to restart", True, WHITE)
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 120))
        self.screen.blit(restart_text, restart_rect)
    
    def start_new_game(self):
        """Start a new game."""
        # Apply difficulty settings
        difficulty_settings = DIFFICULTY_SETTINGS[self.difficulty]
        self.money = difficulty_settings.starting_money
        self.lives = difficulty_settings.starting_lives
        self.current_wave = 0

        # Reset all managers
        self.bloon_manager.clear_all()
        self.tower_manager.clear_all()
        self.projectile_manager.clear_all()
        self.effect_manager.clear_all()
        self.wave_manager.reset()

        # Start gameplay music if playing
        if self.state == GameState.PLAYING:
            from audio_system import MusicType
            self.audio_manager.play_music(MusicType.GAMEPLAY)
    
    def run(self):
        """Main game loop."""
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # Delta time in seconds
            
            self.handle_events()
            self.update(dt)
            self.render()
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()
