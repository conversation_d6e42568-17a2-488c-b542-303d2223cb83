"""
Additional Game Features for Bloons Tower Defense
Includes save/load system, difficulty modes, and other optional features.
"""

import pygame
import json
import os
from typing import Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass, asdict

class Difficulty(Enum):
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"

@dataclass
class DifficultySettings:
    """Settings for different difficulty modes."""
    name: str
    starting_money: int
    starting_lives: int
    bloon_speed_multiplier: float
    bloon_health_multiplier: float
    tower_cost_multiplier: float
    description: str

# Difficulty configurations
DIFFICULTY_SETTINGS = {
    Difficulty.EASY: DifficultySettings(
        "Easy", 850, 150, 0.8, 0.9, 0.85,
        "More money, more lives, slower bloons"
    ),
    Difficulty.MEDIUM: DifficultySettings(
        "Medium", 650, 100, 1.0, 1.0, 1.0,
        "Standard difficulty"
    ),
    Difficulty.HARD: DifficultySettings(
        "Hard", 450, 75, 1.2, 1.1, 1.15,
        "Less money, fewer lives, faster bloons"
    )
}

class SaveLoadManager:
    """Manages saving and loading game state."""
    
    def __init__(self):
        self.save_directory = "saves"
        self.ensure_save_directory()
    
    def ensure_save_directory(self):
        """Create save directory if it doesn't exist."""
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)
    
    def save_game(self, game, filename: str = "quicksave.json") -> bool:
        """Save current game state."""
        try:
            save_data = {
                "version": "1.0",
                "money": game.money,
                "lives": game.lives,
                "current_wave": game.wave_manager.current_wave,
                "wave_in_progress": game.wave_manager.wave_in_progress,
                "difficulty": game.difficulty.value if hasattr(game, 'difficulty') else "medium",
                "towers": self._serialize_towers(game.tower_manager.towers),
                "bloons": self._serialize_bloons(game.bloon_manager.bloons),
                "timestamp": pygame.time.get_ticks()
            }
            
            filepath = os.path.join(self.save_directory, filename)
            with open(filepath, 'w') as f:
                json.dump(save_data, f, indent=2)
            
            return True
        except Exception as e:
            print(f"Failed to save game: {e}")
            return False
    
    def load_game(self, game, filename: str = "quicksave.json") -> bool:
        """Load game state."""
        try:
            filepath = os.path.join(self.save_directory, filename)
            if not os.path.exists(filepath):
                return False
            
            with open(filepath, 'r') as f:
                save_data = json.load(f)
            
            # Restore basic game state
            game.money = save_data.get("money", 650)
            game.lives = save_data.get("lives", 100)
            
            # Set difficulty if available
            difficulty_str = save_data.get("difficulty", "medium")
            try:
                game.difficulty = Difficulty(difficulty_str)
            except:
                game.difficulty = Difficulty.MEDIUM
            
            # Clear current game state
            game.bloon_manager.clear_all()
            game.tower_manager.clear_all()
            game.projectile_manager.clear_all()
            game.effect_manager.clear_all()
            
            # Restore wave state
            game.wave_manager.current_wave = save_data.get("current_wave", 0)
            game.wave_manager.wave_in_progress = save_data.get("wave_in_progress", False)
            
            # Restore towers
            towers_data = save_data.get("towers", [])
            self._deserialize_towers(game.tower_manager, towers_data)
            
            # Restore bloons
            bloons_data = save_data.get("bloons", [])
            self._deserialize_bloons(game.bloon_manager, bloons_data)
            
            return True
        except Exception as e:
            print(f"Failed to load game: {e}")
            return False
    
    def _serialize_towers(self, towers) -> list:
        """Serialize towers for saving."""
        tower_data = []
        for tower in towers:
            data = {
                "type": tower.type.value,
                "position": tower.position,
                "upgrades": tower.upgrades,
                "total_cost": tower.total_cost
            }
            tower_data.append(data)
        return tower_data
    
    def _deserialize_towers(self, tower_manager, towers_data):
        """Deserialize towers from save data."""
        from tower_system import TowerType, Tower
        
        for data in towers_data:
            try:
                tower_type = TowerType(data["type"])
                position = tuple(data["position"])
                
                # Create tower
                tower = Tower(tower_type, position)
                tower.upgrades = data.get("upgrades", {"path1": 0, "path2": 0})
                tower.total_cost = data.get("total_cost", tower.properties.cost)
                
                # Apply upgrades
                for path in ["path1", "path2"]:
                    for _ in range(tower.upgrades[path]):
                        tower.upgrade(path)
                
                tower_manager.towers.append(tower)
            except Exception as e:
                print(f"Failed to deserialize tower: {e}")
    
    def _serialize_bloons(self, bloons) -> list:
        """Serialize bloons for saving."""
        bloon_data = []
        for bloon in bloons:
            data = {
                "type": bloon.type.value,
                "distance_traveled": bloon.distance_traveled,
                "health": bloon.health,
                "camo": bloon.camo
            }
            bloon_data.append(data)
        return bloon_data
    
    def _deserialize_bloons(self, bloon_manager, bloons_data):
        """Deserialize bloons from save data."""
        from bloon_system import BloonType, Bloon
        
        for data in bloons_data:
            try:
                bloon_type = BloonType(data["type"])
                camo = data.get("camo", False)
                
                # Create bloon
                bloon = Bloon(bloon_type, bloon_manager.path_manager, camo)
                bloon.distance_traveled = data.get("distance_traveled", 0)
                bloon.health = data.get("health", bloon.max_health)
                bloon.position = bloon_manager.path_manager.get_position_at_distance(bloon.distance_traveled)
                
                bloon_manager.bloons.append(bloon)
            except Exception as e:
                print(f"Failed to deserialize bloon: {e}")
    
    def get_save_files(self) -> list:
        """Get list of available save files."""
        try:
            files = []
            for filename in os.listdir(self.save_directory):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.save_directory, filename)
                    stat = os.stat(filepath)
                    files.append({
                        'filename': filename,
                        'size': stat.st_size,
                        'modified': stat.st_mtime
                    })
            return sorted(files, key=lambda x: x['modified'], reverse=True)
        except:
            return []

class GameSettings:
    """Manages game settings and preferences."""
    
    def __init__(self):
        self.settings_file = "settings.json"
        self.default_settings = {
            "sound_volume": 0.7,
            "music_volume": 0.5,
            "audio_enabled": True,
            "fullscreen": False,
            "show_fps": False,
            "auto_start_waves": False,
            "difficulty": "medium",
            "controls": {
                "pause": pygame.K_ESCAPE,
                "speed_up": pygame.K_SPACE,
                "sell_tower": pygame.K_s,
                "upgrade_path1": pygame.K_1,
                "upgrade_path2": pygame.K_2
            }
        }
        self.settings = self.load_settings()
    
    def load_settings(self) -> dict:
        """Load settings from file."""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r') as f:
                    loaded_settings = json.load(f)
                # Merge with defaults to ensure all keys exist
                settings = self.default_settings.copy()
                settings.update(loaded_settings)
                return settings
        except Exception as e:
            print(f"Failed to load settings: {e}")
        
        return self.default_settings.copy()
    
    def save_settings(self) -> bool:
        """Save settings to file."""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self.settings, f, indent=2)
            return True
        except Exception as e:
            print(f"Failed to save settings: {e}")
            return False
    
    def get(self, key: str, default=None):
        """Get a setting value."""
        return self.settings.get(key, default)
    
    def set(self, key: str, value):
        """Set a setting value."""
        self.settings[key] = value
    
    def apply_to_game(self, game):
        """Apply settings to game instance."""
        if hasattr(game, 'audio_manager'):
            game.audio_manager.set_sound_volume(self.get("sound_volume", 0.7))
            game.audio_manager.set_music_volume(self.get("music_volume", 0.5))
            game.audio_manager.set_enabled(self.get("audio_enabled", True))

class PerformanceMonitor:
    """Monitors game performance and displays FPS."""
    
    def __init__(self):
        self.fps_history = []
        self.max_history = 60  # Keep 1 second of history at 60 FPS
        self.font = pygame.font.Font(None, 24)
        self.show_fps = False
    
    def update(self, clock):
        """Update performance metrics."""
        fps = clock.get_fps()
        self.fps_history.append(fps)
        
        if len(self.fps_history) > self.max_history:
            self.fps_history.pop(0)
    
    def get_average_fps(self) -> float:
        """Get average FPS over recent history."""
        if not self.fps_history:
            return 0.0
        return sum(self.fps_history) / len(self.fps_history)
    
    def render(self, screen):
        """Render performance information."""
        if not self.show_fps:
            return
        
        avg_fps = self.get_average_fps()
        current_fps = self.fps_history[-1] if self.fps_history else 0
        
        # Choose color based on performance
        if avg_fps >= 55:
            color = (0, 255, 0)  # Green
        elif avg_fps >= 30:
            color = (255, 255, 0)  # Yellow
        else:
            color = (255, 0, 0)  # Red
        
        fps_text = f"FPS: {current_fps:.1f} (avg: {avg_fps:.1f})"
        text_surface = self.font.render(fps_text, True, color)
        screen.blit(text_surface, (10, 10))

class CheatManager:
    """Manages cheat codes for testing and debugging."""
    
    def __init__(self):
        self.enabled = False
        self.input_buffer = ""
        self.max_buffer_length = 20
        
        # Cheat codes
        self.cheats = {
            "money": self._add_money,
            "lives": self._add_lives,
            "wave": self._skip_wave,
            "clear": self._clear_bloons,
            "god": self._god_mode,
            "fast": self._fast_forward
        }
    
    def handle_input(self, event, game):
        """Handle keyboard input for cheat detection."""
        if not self.enabled:
            return
        
        if event.type == pygame.KEYDOWN:
            if event.unicode.isalnum():
                self.input_buffer += event.unicode.lower()
                
                # Keep buffer size manageable
                if len(self.input_buffer) > self.max_buffer_length:
                    self.input_buffer = self.input_buffer[-self.max_buffer_length:]
                
                # Check for cheat codes
                for cheat_code, cheat_function in self.cheats.items():
                    if self.input_buffer.endswith(cheat_code):
                        cheat_function(game)
                        self.input_buffer = ""  # Clear buffer after successful cheat
                        break
    
    def _add_money(self, game):
        """Add money cheat."""
        game.money += 10000
        print("Cheat activated: +$10,000")
    
    def _add_lives(self, game):
        """Add lives cheat."""
        game.lives += 100
        print("Cheat activated: +100 lives")
    
    def _skip_wave(self, game):
        """Skip to next wave cheat."""
        if hasattr(game, 'wave_manager'):
            game.wave_manager.current_wave += 1
            game.bloon_manager.clear_all()
            print(f"Cheat activated: Skipped to wave {game.wave_manager.current_wave + 1}")
    
    def _clear_bloons(self, game):
        """Clear all bloons cheat."""
        if hasattr(game, 'bloon_manager'):
            game.bloon_manager.clear_all()
            print("Cheat activated: Cleared all bloons")
    
    def _god_mode(self, game):
        """God mode cheat (infinite lives)."""
        game.lives = 999999
        print("Cheat activated: God mode")
    
    def _fast_forward(self, game):
        """Fast forward cheat."""
        # This would need to be implemented in the main game loop
        print("Cheat activated: Fast forward (not implemented)")
    
    def enable(self):
        """Enable cheat system."""
        self.enabled = True
        print("Cheat system enabled")
    
    def disable(self):
        """Disable cheat system."""
        self.enabled = False
        self.input_buffer = ""
        print("Cheat system disabled")
