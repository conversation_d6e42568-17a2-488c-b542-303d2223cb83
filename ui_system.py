"""
User Interface System for Bloons Tower Defense
Manages all UI elements including HUD, tower selection, and upgrade panels.
"""

import pygame
import math
from typing import List, Tuple, Optional, Dict, Any, Callable
from enum import Enum
from tower_system import TowerType, TOWER_PROPERTIES, TargetingMode

class UIElement:
    """Base class for UI elements."""
    
    def __init__(self, x: int, y: int, width: int, height: int):
        self.rect = pygame.Rect(x, y, width, height)
        self.visible = True
        self.enabled = True
    
    def handle_event(self, event) -> bool:
        """Handle pygame event. Return True if event was consumed."""
        return False
    
    def update(self, dt: float):
        """Update UI element."""
        pass
    
    def render(self, screen: pygame.Surface):
        """Render UI element."""
        pass

class Button(UIElement):
    """Clickable button UI element."""
    
    def __init__(self, x: int, y: int, width: int, height: int, text: str, 
                 callback: Callable = None, font_size: int = 24):
        super().__init__(x, y, width, height)
        self.text = text
        self.callback = callback
        self.font = pygame.font.Font(None, font_size)
        self.pressed = False
        self.hovered = False
        
        # Colors
        self.bg_color = (200, 200, 200)
        self.hover_color = (220, 220, 220)
        self.pressed_color = (180, 180, 180)
        self.text_color = (0, 0, 0)
        self.border_color = (100, 100, 100)
    
    def handle_event(self, event) -> bool:
        if not self.visible or not self.enabled:
            return False
        
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1 and self.rect.collidepoint(event.pos):
                self.pressed = True
                return True
        elif event.type == pygame.MOUSEBUTTONUP:
            if event.button == 1 and self.pressed:
                self.pressed = False
                if self.rect.collidepoint(event.pos) and self.callback:
                    self.callback()
                return True
        elif event.type == pygame.MOUSEMOTION:
            self.hovered = self.rect.collidepoint(event.pos)
        
        return False
    
    def render(self, screen: pygame.Surface):
        if not self.visible:
            return
        
        # Choose color based on state
        if self.pressed:
            color = self.pressed_color
        elif self.hovered:
            color = self.hover_color
        else:
            color = self.bg_color
        
        # Draw button
        pygame.draw.rect(screen, color, self.rect)
        pygame.draw.rect(screen, self.border_color, self.rect, 2)
        
        # Draw text
        text_surface = self.font.render(self.text, True, self.text_color)
        text_rect = text_surface.get_rect(center=self.rect.center)
        screen.blit(text_surface, text_rect)

class TowerButton(Button):
    """Special button for tower selection."""
    
    def __init__(self, x: int, y: int, tower_type: TowerType, callback: Callable):
        properties = TOWER_PROPERTIES[tower_type]
        super().__init__(x, y, 80, 60, f"${properties.cost}", callback, 16)
        self.tower_type = tower_type
        self.properties = properties
        self.affordable = True
    
    def set_affordable(self, affordable: bool):
        """Set whether player can afford this tower."""
        self.affordable = affordable
        self.enabled = affordable
        
        if not affordable:
            self.bg_color = (150, 150, 150)
            self.text_color = (100, 100, 100)
        else:
            self.bg_color = (200, 200, 200)
            self.text_color = (0, 0, 0)
    
    def render(self, screen: pygame.Surface):
        if not self.visible:
            return
        
        # Draw button background
        super().render(screen)
        
        # Draw tower icon
        center_x = self.rect.centerx
        center_y = self.rect.centery - 10
        
        pygame.draw.circle(screen, self.properties.color, 
                          (center_x, center_y), self.properties.size // 2)
        pygame.draw.circle(screen, (0, 0, 0), 
                          (center_x, center_y), self.properties.size // 2, 1)
        
        # Draw tower name
        name_font = pygame.font.Font(None, 14)
        name_surface = name_font.render(self.properties.name.replace("_", " ").title(), 
                                       True, self.text_color)
        name_rect = name_surface.get_rect(centerx=center_x, y=self.rect.bottom - 15)
        screen.blit(name_surface, name_rect)

class UIManager:
    """Manages all UI elements and interactions."""
    
    def __init__(self, game):
        self.game = game
        self.elements: List[UIElement] = []
        
        # Fonts
        self.font_small = pygame.font.Font(None, 20)
        self.font_medium = pygame.font.Font(None, 28)
        self.font_large = pygame.font.Font(None, 36)
        
        # UI state
        self.selected_tower_type = None
        self.placing_tower = False
        self.show_upgrade_panel = False
        
        # Create UI elements
        self._create_ui_elements()
    
    def _create_ui_elements(self):
        """Create all UI elements."""
        # Sidebar background (right side of screen)
        self.sidebar_rect = pygame.Rect(1000, 0, 200, 800)
        
        # Tower selection buttons
        tower_types = list(TowerType)
        for i, tower_type in enumerate(tower_types):
            x = 1010
            y = 50 + i * 70
            button = TowerButton(x, y, tower_type, 
                               lambda tt=tower_type: self._select_tower_type(tt))
            self.elements.append(button)
        
        # Wave control buttons
        self.start_wave_button = Button(1010, 350, 80, 30, "Start Wave", 
                                       self._start_wave, 18)
        self.elements.append(self.start_wave_button)
        
        # Speed control buttons
        self.speed_1x_button = Button(1010, 400, 35, 25, "1x", 
                                     lambda: self._set_speed(1.0), 16)
        self.speed_2x_button = Button(1050, 400, 35, 25, "2x", 
                                     lambda: self._set_speed(2.0), 16)
        self.speed_3x_button = Button(1090, 400, 35, 25, "3x", 
                                     lambda: self._set_speed(3.0), 16)
        
        self.elements.extend([self.speed_1x_button, self.speed_2x_button, self.speed_3x_button])
        
        # Targeting mode buttons (shown when tower is selected)
        self.targeting_buttons = []
        targeting_modes = list(TargetingMode)
        for i, mode in enumerate(targeting_modes):
            x = 1010 + (i % 2) * 45
            y = 500 + (i // 2) * 30
            button = Button(x, y, 40, 25, mode.value.title()[:4], 
                           lambda m=mode: self._set_targeting_mode(m), 14)
            button.visible = False
            self.targeting_buttons.append(button)
            self.elements.append(button)
        
        # Upgrade buttons (shown when tower is selected)
        self.upgrade_path1_button = Button(1010, 600, 80, 30, "Upgrade 1", 
                                          lambda: self._upgrade_tower("path1"), 16)
        self.upgrade_path2_button = Button(1010, 635, 80, 30, "Upgrade 2", 
                                          lambda: self._upgrade_tower("path2"), 16)
        self.sell_button = Button(1010, 670, 80, 30, "Sell", 
                                 self._sell_tower, 16)
        
        self.upgrade_path1_button.visible = False
        self.upgrade_path2_button.visible = False
        self.sell_button.visible = False
        
        self.elements.extend([self.upgrade_path1_button, self.upgrade_path2_button, self.sell_button])
    
    def _select_tower_type(self, tower_type: TowerType):
        """Select a tower type for placement."""
        if self.game.money >= TOWER_PROPERTIES[tower_type].cost:
            self.selected_tower_type = tower_type
            self.placing_tower = True
            # Deselect any selected tower
            if self.game.tower_manager.selected_tower:
                self.game.tower_manager.selected_tower.selected = False
                self.game.tower_manager.selected_tower = None
            self._update_tower_selection_ui()
    
    def _start_wave(self):
        """Start the next wave."""
        if self.game.wave_manager.can_start_next_wave():
            self.game.wave_manager.start_wave()
    
    def _set_speed(self, speed: float):
        """Set game speed multiplier."""
        # This would be implemented in the main game loop
        pass
    
    def _set_targeting_mode(self, mode: TargetingMode):
        """Set targeting mode for selected tower."""
        if self.game.tower_manager.selected_tower:
            self.game.tower_manager.selected_tower.targeting_mode = mode
    
    def _upgrade_tower(self, path: str):
        """Upgrade selected tower."""
        tower = self.game.tower_manager.selected_tower
        if tower:
            cost = tower.get_upgrade_cost(path)
            if self.game.money >= cost and tower.can_upgrade(path):
                if self.game.tower_manager.upgrade_tower(tower, path):
                    self.game.money -= cost
                    self._update_tower_selection_ui()
    
    def _sell_tower(self):
        """Sell selected tower."""
        tower = self.game.tower_manager.selected_tower
        if tower:
            sell_value = self.game.tower_manager.sell_tower(tower)
            self.game.money += sell_value
            self._update_tower_selection_ui()
    
    def _update_tower_selection_ui(self):
        """Update UI elements based on tower selection."""
        selected_tower = self.game.tower_manager.selected_tower
        
        # Update targeting buttons
        for button in self.targeting_buttons:
            button.visible = selected_tower is not None
        
        # Update upgrade and sell buttons
        if selected_tower:
            self.upgrade_path1_button.visible = True
            self.upgrade_path2_button.visible = True
            self.sell_button.visible = True
            
            # Update upgrade button text and affordability
            path1_cost = selected_tower.get_upgrade_cost("path1")
            path2_cost = selected_tower.get_upgrade_cost("path2")
            
            if path1_cost > 0:
                self.upgrade_path1_button.text = f"Up1 ${path1_cost}"
                self.upgrade_path1_button.enabled = (self.game.money >= path1_cost and 
                                                   selected_tower.can_upgrade("path1"))
            else:
                self.upgrade_path1_button.text = "Max"
                self.upgrade_path1_button.enabled = False
            
            if path2_cost > 0:
                self.upgrade_path2_button.text = f"Up2 ${path2_cost}"
                self.upgrade_path2_button.enabled = (self.game.money >= path2_cost and 
                                                   selected_tower.can_upgrade("path2"))
            else:
                self.upgrade_path2_button.text = "Max"
                self.upgrade_path2_button.enabled = False
            
            self.sell_button.text = f"Sell ${selected_tower.get_sell_value()}"
        else:
            self.upgrade_path1_button.visible = False
            self.upgrade_path2_button.visible = False
            self.sell_button.visible = False
    
    def handle_event(self, event):
        """Handle pygame events."""
        # Let UI elements handle events first
        for element in self.elements:
            if element.handle_event(event):
                return True
        
        # Handle game-specific events
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                mouse_pos = event.pos
                
                # Check if clicking in game area (not sidebar)
                if mouse_pos[0] < 1000:
                    if self.placing_tower and self.selected_tower_type:
                        # Try to place tower
                        if self.game.tower_manager.can_place_tower(mouse_pos, self.selected_tower_type):
                            cost = TOWER_PROPERTIES[self.selected_tower_type].cost
                            if self.game.money >= cost:
                                if self.game.tower_manager.place_tower(self.selected_tower_type, mouse_pos):
                                    self.game.money -= cost
                                    self.placing_tower = False
                                    self.selected_tower_type = None
                    else:
                        # Try to select tower
                        self.game.tower_manager.select_tower(mouse_pos)
                        self._update_tower_selection_ui()
            elif event.button == 3:  # Right click
                # Cancel tower placement
                self.placing_tower = False
                self.selected_tower_type = None
        
        return False
    
    def update(self, dt: float):
        """Update UI elements."""
        for element in self.elements:
            element.update(dt)
        
        # Update tower button affordability
        for element in self.elements:
            if isinstance(element, TowerButton):
                affordable = self.game.money >= element.properties.cost
                element.set_affordable(affordable)
        
        # Update wave button
        wave_info = self.game.wave_manager.get_wave_info()
        if wave_info['can_start_next']:
            self.start_wave_button.text = f"Wave {wave_info['wave_number']}"
            self.start_wave_button.enabled = True
        else:
            if wave_info['in_progress']:
                self.start_wave_button.text = "In Progress"
            else:
                self.start_wave_button.text = "Wave Ready"
            self.start_wave_button.enabled = False
    
    def render(self, screen: pygame.Surface):
        """Render all UI elements."""
        # Draw sidebar background
        pygame.draw.rect(screen, (240, 240, 240), self.sidebar_rect)
        pygame.draw.line(screen, (0, 0, 0), (1000, 0), (1000, 800), 2)
        
        # Render UI elements
        for element in self.elements:
            element.render(screen)
        
        # Render HUD information
        self._render_hud(screen)
        
        # Render tower placement preview
        if self.placing_tower and self.selected_tower_type:
            self._render_tower_preview(screen)
    
    def _render_hud(self, screen: pygame.Surface):
        """Render heads-up display information."""
        # Money
        money_text = self.font_medium.render(f"Money: ${self.game.money}", True, (0, 0, 0))
        screen.blit(money_text, (1010, 10))
        
        # Lives
        lives_text = self.font_medium.render(f"Lives: {self.game.lives}", True, (255, 0, 0))
        screen.blit(lives_text, (1010, 30))
        
        # Wave information
        wave_info = self.game.wave_manager.get_wave_info()
        wave_text = self.font_small.render(f"Wave: {wave_info['wave_number']}", True, (0, 0, 0))
        screen.blit(wave_text, (1010, 320))
        
        if wave_info['in_progress']:
            remaining_text = self.font_small.render(
                f"Spawns: {wave_info['spawns_remaining']}", True, (0, 0, 0))
            screen.blit(remaining_text, (1010, 335))
        
        # Selected tower info
        selected_tower = self.game.tower_manager.selected_tower
        if selected_tower:
            self._render_tower_info(screen, selected_tower)
    
    def _render_tower_info(self, screen: pygame.Surface, tower):
        """Render information about selected tower."""
        y_offset = 450
        
        # Tower name
        name_text = self.font_small.render(tower.properties.name.replace("_", " ").title(), 
                                          True, (0, 0, 0))
        screen.blit(name_text, (1010, y_offset))
        
        # Tower stats
        stats = [
            f"Damage: {tower.damage}",
            f"Range: {int(tower.range)}",
            f"Speed: {tower.fire_rate:.1f}/s"
        ]
        
        for i, stat in enumerate(stats):
            stat_text = self.font_small.render(stat, True, (0, 0, 0))
            screen.blit(stat_text, (1010, y_offset + 15 + i * 12))
    
    def _render_tower_preview(self, screen: pygame.Surface):
        """Render tower placement preview."""
        mouse_pos = pygame.mouse.get_pos()
        
        if mouse_pos[0] < 1000:  # Only show in game area
            properties = TOWER_PROPERTIES[self.selected_tower_type]
            
            # Check if position is valid
            can_place = self.game.tower_manager.can_place_tower(mouse_pos, self.selected_tower_type)
            
            # Draw range circle
            range_color = (0, 255, 0, 50) if can_place else (255, 0, 0, 50)
            pygame.draw.circle(screen, range_color, mouse_pos, int(properties.range), 2)
            
            # Draw tower preview
            tower_color = properties.color if can_place else (255, 0, 0)
            pygame.draw.circle(screen, tower_color, mouse_pos, properties.size)
            pygame.draw.circle(screen, (0, 0, 0), mouse_pos, properties.size, 2)
