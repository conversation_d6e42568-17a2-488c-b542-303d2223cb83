"""
Wave Management System for Bloons Tower Defense
Manages wave spawning, progression, and difficulty scaling.
"""

import pygame
import math
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from bloon_system import BloonType, BloonManager

@dataclass
class BloonSpawn:
    """Represents a bloon spawn in a wave."""
    bloon_type: BloonType
    count: int
    delay: float  # Delay between spawns of this type
    camo_chance: float = 0.0  # Chance for bloon to be camo

@dataclass
class Wave:
    """Represents a complete wave definition."""
    wave_number: int
    spawns: List[BloonSpawn]
    pre_wave_delay: float = 2.0  # Delay before wave starts
    reward_multiplier: float = 1.0

class WaveManager:
    """Manages wave spawning and progression."""
    
    def __init__(self, bloon_manager: BloonManager):
        self.bloon_manager = bloon_manager
        self.current_wave = 0
        self.wave_in_progress = False
        self.wave_complete = False
        self.auto_start = False
        
        # Spawning state
        self.current_spawn_index = 0
        self.current_spawn_count = 0
        self.spawn_timer = 0.0
        self.pre_wave_timer = 0.0
        
        # Wave definitions
        self.waves = self._generate_waves()
        
        # State tracking
        self.total_bloons_spawned = 0
        self.total_bloons_in_wave = 0
    
    def _generate_waves(self) -> List[Wave]:
        """Generate all wave definitions."""
        waves = []
        
        # Early waves (1-10) - Basic bloons
        for i in range(1, 11):
            spawns = []
            
            if i == 1:
                spawns.append(BloonSpawn(BloonType.RED, 20, 0.5))
            elif i == 2:
                spawns.append(BloonSpawn(BloonType.RED, 35, 0.4))
            elif i == 3:
                spawns.append(BloonSpawn(BloonType.RED, 25, 0.4))
                spawns.append(BloonSpawn(BloonType.BLUE, 15, 0.5))
            elif i == 4:
                spawns.append(BloonSpawn(BloonType.RED, 30, 0.3))
                spawns.append(BloonSpawn(BloonType.BLUE, 20, 0.4))
            elif i == 5:
                spawns.append(BloonSpawn(BloonType.RED, 20, 0.3))
                spawns.append(BloonSpawn(BloonType.BLUE, 25, 0.3))
                spawns.append(BloonSpawn(BloonType.GREEN, 10, 0.5))
            elif i == 6:
                spawns.append(BloonSpawn(BloonType.BLUE, 30, 0.3))
                spawns.append(BloonSpawn(BloonType.GREEN, 15, 0.4))
            elif i == 7:
                spawns.append(BloonSpawn(BloonType.GREEN, 25, 0.3))
                spawns.append(BloonSpawn(BloonType.YELLOW, 8, 0.5))
            elif i == 8:
                spawns.append(BloonSpawn(BloonType.GREEN, 30, 0.25))
                spawns.append(BloonSpawn(BloonType.YELLOW, 12, 0.4))
            elif i == 9:
                spawns.append(BloonSpawn(BloonType.YELLOW, 20, 0.3))
                spawns.append(BloonSpawn(BloonType.PINK, 5, 0.6))
            elif i == 10:
                spawns.append(BloonSpawn(BloonType.YELLOW, 25, 0.25))
                spawns.append(BloonSpawn(BloonType.PINK, 10, 0.4))
            
            waves.append(Wave(i, spawns))
        
        # Mid waves (11-25) - Introduce special bloons
        for i in range(11, 26):
            spawns = []
            
            if i == 11:
                spawns.append(BloonSpawn(BloonType.PINK, 15, 0.3))
                spawns.append(BloonSpawn(BloonType.BLACK, 3, 1.0))
            elif i == 12:
                spawns.append(BloonSpawn(BloonType.PINK, 20, 0.25))
                spawns.append(BloonSpawn(BloonType.WHITE, 3, 1.0))
            elif i == 13:
                spawns.append(BloonSpawn(BloonType.YELLOW, 30, 0.2))
                spawns.append(BloonSpawn(BloonType.BLACK, 5, 0.8))
                spawns.append(BloonSpawn(BloonType.WHITE, 5, 0.8))
            elif i == 14:
                spawns.append(BloonSpawn(BloonType.PINK, 25, 0.2))
                spawns.append(BloonSpawn(BloonType.LEAD, 2, 2.0))
            elif i == 15:
                spawns.append(BloonSpawn(BloonType.BLACK, 10, 0.4))
                spawns.append(BloonSpawn(BloonType.WHITE, 10, 0.4))
                spawns.append(BloonSpawn(BloonType.LEAD, 3, 1.5))
            elif i >= 16 and i <= 20:
                # Scaling difficulty
                base_count = 20 + (i - 16) * 5
                spawns.append(BloonSpawn(BloonType.PINK, base_count, 0.15))
                spawns.append(BloonSpawn(BloonType.BLACK, i - 10, 0.5))
                spawns.append(BloonSpawn(BloonType.WHITE, i - 10, 0.5))
                spawns.append(BloonSpawn(BloonType.LEAD, i - 13, 1.0))
                
                # Introduce camo
                if i >= 18:
                    spawns.append(BloonSpawn(BloonType.YELLOW, 10, 0.3, camo_chance=0.5))
            elif i >= 21 and i <= 25:
                # Higher tier bloons
                spawns.append(BloonSpawn(BloonType.PINK, 30, 0.1))
                spawns.append(BloonSpawn(BloonType.ZEBRA, i - 18, 0.8))
                spawns.append(BloonSpawn(BloonType.RAINBOW, i - 20, 1.0))
                spawns.append(BloonSpawn(BloonType.LEAD, i - 15, 0.6))
                
                if i >= 23:
                    spawns.append(BloonSpawn(BloonType.GREEN, 20, 0.2, camo_chance=0.7))
            
            waves.append(Wave(i, spawns))
        
        # Late waves (26-40) - Ceramic and MOAB introduction
        for i in range(26, 41):
            spawns = []
            
            if i == 26:
                spawns.append(BloonSpawn(BloonType.RAINBOW, 15, 0.4))
                spawns.append(BloonSpawn(BloonType.CERAMIC, 2, 3.0))
            elif i >= 27 and i <= 30:
                spawns.append(BloonSpawn(BloonType.RAINBOW, 20, 0.3))
                spawns.append(BloonSpawn(BloonType.CERAMIC, i - 24, 2.0))
                spawns.append(BloonSpawn(BloonType.LEAD, 10, 0.4))
            elif i >= 31 and i <= 35:
                spawns.append(BloonSpawn(BloonType.CERAMIC, i - 20, 1.5))
                spawns.append(BloonSpawn(BloonType.RAINBOW, 15, 0.2, camo_chance=0.6))
                
                if i >= 33:
                    spawns.append(BloonSpawn(BloonType.LEAD, 15, 0.3, camo_chance=0.4))
            elif i >= 36 and i <= 39:
                spawns.append(BloonSpawn(BloonType.CERAMIC, i - 15, 1.0))
                spawns.append(BloonSpawn(BloonType.RAINBOW, 20, 0.15, camo_chance=0.8))
            elif i == 40:
                # First MOAB wave
                spawns.append(BloonSpawn(BloonType.MOAB, 1, 5.0))
                spawns.append(BloonSpawn(BloonType.CERAMIC, 10, 0.8))
            
            waves.append(Wave(i, spawns))
        
        # MOAB waves (41+) - Procedurally generated
        for i in range(41, 101):
            spawns = self._generate_moab_wave(i)
            waves.append(Wave(i, spawns))
        
        return waves
    
    def _generate_moab_wave(self, wave_num: int) -> List[BloonSpawn]:
        """Generate a MOAB-era wave procedurally."""
        spawns = []
        
        # Base difficulty scaling
        difficulty = wave_num - 40
        
        # MOAB spawns
        if wave_num >= 40:
            moab_count = 1 + (difficulty // 10)
            spawns.append(BloonSpawn(BloonType.MOAB, moab_count, 8.0))
        
        # BFG spawns
        if wave_num >= 60:
            bfg_count = 1 + ((difficulty - 20) // 15)
            spawns.append(BloonSpawn(BloonType.BFG, bfg_count, 15.0))
        
        # ZOMG spawns
        if wave_num >= 80:
            zomg_count = 1 + ((difficulty - 40) // 20)
            spawns.append(BloonSpawn(BloonType.ZOMG, zomg_count, 25.0))
        
        # BAD spawns
        if wave_num >= 100:
            bad_count = 1 + ((difficulty - 60) // 30)
            spawns.append(BloonSpawn(BloonType.BAD, bad_count, 40.0))
        
        # Fill with ceramics and other bloons
        ceramic_count = 10 + difficulty
        spawns.append(BloonSpawn(BloonType.CERAMIC, ceramic_count, 0.5))
        
        # Camo bloons
        camo_count = 5 + (difficulty // 2)
        spawns.append(BloonSpawn(BloonType.RAINBOW, camo_count, 0.3, camo_chance=1.0))
        
        return spawns
    
    def start_wave(self):
        """Start the current wave."""
        if self.wave_in_progress or self.current_wave >= len(self.waves):
            return False
        
        self.wave_in_progress = True
        self.wave_complete = False
        self.current_spawn_index = 0
        self.current_spawn_count = 0
        self.spawn_timer = 0.0
        self.pre_wave_timer = self.waves[self.current_wave].pre_wave_delay
        
        # Calculate total bloons in wave
        self.total_bloons_in_wave = sum(spawn.count for spawn in self.waves[self.current_wave].spawns)
        self.total_bloons_spawned = 0
        
        return True
    
    def update(self, dt: float) -> Dict[str, Any]:
        """Update wave spawning logic."""
        result = {
            'wave_started': False,
            'wave_completed': False,
            'bloons_spawned': 0,
            'money_bonus': 0
        }
        
        if not self.wave_in_progress:
            return result
        
        current_wave_data = self.waves[self.current_wave]
        
        # Handle pre-wave delay
        if self.pre_wave_timer > 0:
            self.pre_wave_timer -= dt
            if self.pre_wave_timer <= 0:
                result['wave_started'] = True
            return result
        
        # Check if all spawns are complete
        if self.current_spawn_index >= len(current_wave_data.spawns):
            # Check if all bloons have been spawned and cleared
            if (self.total_bloons_spawned >= self.total_bloons_in_wave and 
                len(self.bloon_manager.bloons) == 0):
                self._complete_wave()
                result['wave_completed'] = True
                result['money_bonus'] = self._calculate_wave_bonus()
            return result
        
        # Handle current spawn
        current_spawn = current_wave_data.spawns[self.current_spawn_index]
        
        # Update spawn timer
        self.spawn_timer -= dt
        
        if self.spawn_timer <= 0:
            # Spawn a bloon
            camo = (current_spawn.camo_chance > 0 and 
                   pygame.time.get_ticks() % 100 < current_spawn.camo_chance * 100)
            
            self.bloon_manager.add_bloon(current_spawn.bloon_type, camo)
            self.current_spawn_count += 1
            self.total_bloons_spawned += 1
            result['bloons_spawned'] = 1
            
            # Reset timer
            self.spawn_timer = current_spawn.delay
            
            # Check if this spawn type is complete
            if self.current_spawn_count >= current_spawn.count:
                self.current_spawn_index += 1
                self.current_spawn_count = 0
                self.spawn_timer = 0  # Start next spawn immediately
        
        return result
    
    def _complete_wave(self):
        """Complete the current wave."""
        self.wave_in_progress = False
        self.wave_complete = True
        self.current_wave += 1
    
    def _calculate_wave_bonus(self) -> int:
        """Calculate bonus money for completing a wave."""
        base_bonus = 100
        wave_multiplier = 1 + (self.current_wave * 0.02)  # 2% increase per wave
        return int(base_bonus * wave_multiplier)
    
    def can_start_next_wave(self) -> bool:
        """Check if the next wave can be started."""
        return (not self.wave_in_progress and 
                self.current_wave < len(self.waves) and
                len(self.bloon_manager.bloons) == 0)
    
    def get_wave_info(self) -> Dict[str, Any]:
        """Get information about the current wave."""
        if self.current_wave >= len(self.waves):
            return {
                'wave_number': self.current_wave,
                'total_waves': len(self.waves),
                'in_progress': False,
                'complete': True,
                'spawns_remaining': 0,
                'bloons_remaining': 0
            }
        
        spawns_remaining = 0
        if self.wave_in_progress and self.current_spawn_index < len(self.waves[self.current_wave].spawns):
            for i in range(self.current_spawn_index, len(self.waves[self.current_wave].spawns)):
                spawn = self.waves[self.current_wave].spawns[i]
                if i == self.current_spawn_index:
                    spawns_remaining += spawn.count - self.current_spawn_count
                else:
                    spawns_remaining += spawn.count
        
        return {
            'wave_number': self.current_wave + 1,  # Display as 1-indexed
            'total_waves': len(self.waves),
            'in_progress': self.wave_in_progress,
            'complete': self.wave_complete,
            'spawns_remaining': spawns_remaining,
            'bloons_remaining': len(self.bloon_manager.bloons),
            'can_start_next': self.can_start_next_wave()
        }
    
    def skip_to_wave(self, wave_number: int):
        """Skip to a specific wave (for testing)."""
        if 0 <= wave_number < len(self.waves):
            self.current_wave = wave_number
            self.wave_in_progress = False
            self.wave_complete = False
            self.bloon_manager.clear_all()
    
    def reset(self):
        """Reset wave manager to beginning."""
        self.current_wave = 0
        self.wave_in_progress = False
        self.wave_complete = False
        self.current_spawn_index = 0
        self.current_spawn_count = 0
        self.spawn_timer = 0.0
        self.pre_wave_timer = 0.0
        self.total_bloons_spawned = 0
        self.total_bloons_in_wave = 0
